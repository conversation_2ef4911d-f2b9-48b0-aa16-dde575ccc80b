#!/usr/bin/env python3
"""
Test script to verify that all the improved functions work correctly
before running the full notebook.
"""

import numpy as np
import matplotlib.pyplot as plt
from hw4_utils import *

def test_improved_functions():
    """Test all the improved functions to ensure they work correctly."""
    
    print("=== Testing Improved HW4 Functions ===\n")
    
    # Test 1: Data loading
    print("1. Testing data loading...")
    try:
        ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(
            '../data/lab4/AB4_ecg.txt', '../data/lab4/AB4_acc.txt', duration_minutes=1
        )
        fs_ecg = 1 / np.mean(np.diff(ecg_time))
        fs_bcg = 1 / np.mean(np.diff(bcg_time))
        print(f"   ✓ Data loaded successfully")
        print(f"   ✓ ECG: {len(ecg_signal)} samples at {fs_ecg:.1f} Hz")
        print(f"   ✓ BCG: {len(bcg_signal)} samples at {fs_bcg:.1f} Hz")
    except Exception as e:
        print(f"   ✗ Error loading data: {e}")
        return False
    
    # Test 2: Signal preprocessing
    print("\n2. Testing signal preprocessing...")
    try:
        ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
        bcg_filtered = preprocess_signal(bcg_signal, lowcut=1, highcut=20, fs=fs_bcg)
        
        ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet='db4', level=6)
        bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet='db6', level=8)
        
        # Ensure same length
        min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
        min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))
        
        ecg_dwt = ecg_dwt[:min_len_ecg]
        bcg_dwt = bcg_dwt[:min_len_bcg]
        ecg_time_dwt = ecg_time[:min_len_ecg]
        bcg_time_dwt = bcg_time[:min_len_bcg]
        
        print(f"   ✓ Preprocessing completed successfully")
    except Exception as e:
        print(f"   ✗ Error in preprocessing: {e}")
        return False
    
    # Test 3: Improved peak detection
    print("\n3. Testing improved peak detection...")
    try:
        # ECG peaks
        ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
        print(f"   ✓ ECG R peaks: {len(ecg_peaks)} detected")
        
        # BCG peaks - Hilbert method
        bcg_peaks_hilbert, envelope_hilbert = find_j_peaks_bcg(bcg_dwt, fs_bcg, method='hilbert')
        print(f"   ✓ BCG J peaks (Hilbert): {len(bcg_peaks_hilbert)} detected")
        
        # BCG peaks - Rectify method
        bcg_peaks_rectify, envelope_rectify = find_j_peaks_bcg(bcg_dwt, fs_bcg, method='rectify')
        print(f"   ✓ BCG J peaks (Rectify+LP): {len(bcg_peaks_rectify)} detected")
        
        # Verify envelope shapes
        assert len(envelope_hilbert) == len(bcg_dwt), "Hilbert envelope length mismatch"
        assert len(envelope_rectify) == len(bcg_dwt), "Rectify envelope length mismatch"
        print(f"   ✓ Envelope calculations verified")
        
    except Exception as e:
        print(f"   ✗ Error in peak detection: {e}")
        return False
    
    # Test 4: RR intervals and heart rate calculation
    print("\n4. Testing RR intervals and heart rate calculation...")
    try:
        # Calculate RR intervals
        rr_ecg = calculate_rr_intervals(ecg_peaks, fs_ecg)
        rr_bcg_hilbert = calculate_rr_intervals(bcg_peaks_hilbert, fs_bcg)
        rr_bcg_rectify = calculate_rr_intervals(bcg_peaks_rectify, fs_bcg)
        
        print(f"   ✓ RR intervals calculated")
        print(f"     ECG: {len(rr_ecg)} intervals")
        print(f"     BCG (Hilbert): {len(rr_bcg_hilbert)} intervals")
        print(f"     BCG (Rectify): {len(rr_bcg_rectify)} intervals")
        
        # Calculate heart rates (original and smoothed)
        hr_ecg = calculate_heart_rate(rr_ecg)
        hr_bcg_hilbert = calculate_heart_rate(rr_bcg_hilbert)
        hr_bcg_hilbert_smooth = calculate_heart_rate(rr_bcg_hilbert, smooth=True)
        hr_bcg_rectify = calculate_heart_rate(rr_bcg_rectify)
        hr_bcg_rectify_smooth = calculate_heart_rate(rr_bcg_rectify, smooth=True)
        
        print(f"   ✓ Heart rates calculated")
        if len(hr_ecg) > 0:
            print(f"     ECG HR: {np.mean(hr_ecg):.1f} ± {np.std(hr_ecg):.1f} bpm")
        if len(hr_bcg_hilbert) > 0:
            print(f"     BCG (Hilbert): {np.mean(hr_bcg_hilbert):.1f} ± {np.std(hr_bcg_hilbert):.1f} bpm")
            print(f"     BCG (Hilbert, smoothed): {np.mean(hr_bcg_hilbert_smooth):.1f} ± {np.std(hr_bcg_hilbert_smooth):.1f} bpm")
        if len(hr_bcg_rectify) > 0:
            print(f"     BCG (Rectify): {np.mean(hr_bcg_rectify):.1f} ± {np.std(hr_bcg_rectify):.1f} bpm")
            print(f"     BCG (Rectify, smoothed): {np.mean(hr_bcg_rectify_smooth):.1f} ± {np.std(hr_bcg_rectify_smooth):.1f} bpm")
        
    except Exception as e:
        print(f"   ✗ Error in RR/HR calculation: {e}")
        return False
    
    # Test 5: Smoothing effectiveness
    print("\n5. Testing smoothing effectiveness...")
    try:
        if len(hr_bcg_hilbert) > 0:
            std_reduction = ((np.std(hr_bcg_hilbert) - np.std(hr_bcg_hilbert_smooth)) / np.std(hr_bcg_hilbert)) * 100
            print(f"   ✓ Hilbert method std reduction: {std_reduction:.1f}%")
        
        if len(hr_bcg_rectify) > 0:
            std_reduction_rect = ((np.std(hr_bcg_rectify) - np.std(hr_bcg_rectify_smooth)) / np.std(hr_bcg_rectify)) * 100
            print(f"   ✓ Rectify method std reduction: {std_reduction_rect:.1f}%")
        
        # Test direct smoothing function
        if len(hr_bcg_hilbert) >= 3:
            hr_manual_smooth = smooth_hr_curve(hr_bcg_hilbert, window_size=3)
            assert len(hr_manual_smooth) == len(hr_bcg_hilbert), "Manual smoothing length mismatch"
            print(f"   ✓ Manual smoothing function verified")
        
    except Exception as e:
        print(f"   ✗ Error in smoothing test: {e}")
        return False
    
    # Test 6: HRV metrics
    print("\n6. Testing HRV metrics...")
    try:
        if len(rr_ecg) > 1:
            hrv_ecg = calculate_hrv_metrics(rr_ecg)
            print(f"   ✓ ECG HRV metrics: {len(hrv_ecg)} calculated")
        
        if len(rr_bcg_hilbert) > 1:
            hrv_bcg = calculate_hrv_metrics(rr_bcg_hilbert)
            print(f"   ✓ BCG HRV metrics: {len(hrv_bcg)} calculated")
        
        # Test frequency domain if enough data
        if len(rr_ecg) > 10:
            rr_times_ecg = np.cumsum(np.concatenate([[0], rr_ecg[:-1]])) / 1000
            freq_hrv_ecg, freqs_ecg, psd_ecg = calculate_frequency_domain_hrv(rr_ecg, rr_times_ecg)
            print(f"   ✓ ECG frequency domain HRV calculated")
        
    except Exception as e:
        print(f"   ✗ Error in HRV calculation: {e}")
        return False
    
    # Test 7: Plotting functions
    print("\n7. Testing plotting functions...")
    try:
        # Test envelope plotting
        if len(bcg_peaks_hilbert) > 0:
            fig = plot_bcg_envelope_detection(
                bcg_time_dwt[:1000], bcg_dwt[:1000], envelope_hilbert[:1000], 
                bcg_peaks_hilbert[bcg_peaks_hilbert < 1000], 
                "Test BCG Envelope Detection"
            )
            plt.close(fig)
            print(f"   ✓ Envelope detection plot function works")
        
        # Test HR smoothing plot
        if len(hr_bcg_hilbert) > 0:
            hr_times_bcg = bcg_time_dwt[bcg_peaks_hilbert[1:]] if len(bcg_peaks_hilbert) > 1 else []
            if len(hr_times_bcg) > 0:
                fig = plot_hr_smoothing_comparison(
                    hr_times_bcg, hr_bcg_hilbert, hr_bcg_hilbert_smooth,
                    "Test HR Smoothing"
                )
                plt.close(fig)
                print(f"   ✓ HR smoothing plot function works")
        
    except Exception as e:
        print(f"   ✗ Error in plotting functions: {e}")
        return False
    
    print("\n=== All Tests Passed Successfully! ===")
    print("\nThe notebook should now run correctly with all improved methods.")
    print("\nKey improvements verified:")
    print("✓ Envelope-based BCG peak detection (Hilbert & Rectify+LP)")
    print("✓ 3-beat moving average heart rate smoothing")
    print("✓ Comprehensive comparison between methods")
    print("✓ Enhanced visualization functions")
    
    return True

if __name__ == "__main__":
    test_improved_functions()
