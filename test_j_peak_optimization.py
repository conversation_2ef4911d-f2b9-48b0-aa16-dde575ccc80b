#!/usr/bin/env python3
"""
Simple test script to optimize J peak detection parameters.
"""

import numpy as np
import matplotlib.pyplot as plt
from hw4_utils import *

def test_j_peak_optimization():
    """Test J peak detection with different parameters."""
    
    print("=== J Peak Detection Parameter Optimization ===")
    
    # Load data
    print("Loading data...")
    ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(
        '../data/lab4/AB4_ecg.txt', '../data/lab4/AB4_acc.txt', duration_minutes=5
    )
    fs_ecg = 1 / np.mean(np.diff(ecg_time))
    fs_bcg = 1 / np.mean(np.diff(bcg_time))
    print(f"Data loaded: ECG {fs_ecg:.1f} Hz, BCG {fs_bcg:.1f} Hz")
    
    # Preprocess
    print("Preprocessing...")
    ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
    bcg_filtered = preprocess_signal(bcg_signal, lowcut=0.5, highcut=20, fs=fs_bcg)
    
    ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet='db4', level=4)
    bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet='db4', level=4)
    
    min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
    min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))
    
    ecg_dwt = ecg_dwt[:min_len_ecg]
    bcg_dwt = bcg_dwt[:min_len_bcg]
    
    # Get ECG reference
    ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
    ecg_count = len(ecg_peaks)
    print(f"ECG peaks detected: {ecg_count}")
    
    # Test parameters
    print("Testing different parameter combinations...")
    
    # Parameter ranges
    threshold_factors = [0.8, 1.0, 1.2, 1.5, 1.8, 2.0]
    min_distances = [0.3, 0.35, 0.4, 0.45, 0.5]
    methods = ['hilbert', 'rectify']
    
    best_score = float('inf')
    best_params = None
    results = []
    
    total_tests = len(threshold_factors) * len(min_distances) * len(methods)
    test_count = 0
    
    for method in methods:
        for threshold_factor in threshold_factors:
            for min_distance in min_distances:
                test_count += 1
                
                try:
                    # Test with positive amplitude filtering
                    bcg_peaks, bcg_envelope = find_j_peaks_bcg(
                        bcg_dwt, fs_bcg, 
                        method=method,
                        threshold_factor=threshold_factor,
                        min_distance_sec=min_distance
                    )
                    
                    bcg_count = len(bcg_peaks)
                    count_diff = abs(bcg_count - ecg_count)
                    count_ratio = bcg_count / ecg_count if ecg_count > 0 else 0
                    
                    # Score: prioritize count difference and ratio close to 1.0
                    score = count_diff + abs(count_ratio - 1.0) * 20
                    
                    results.append({
                        'method': method,
                        'threshold_factor': threshold_factor,
                        'min_distance': min_distance,
                        'bcg_count': bcg_count,
                        'count_diff': count_diff,
                        'count_ratio': count_ratio,
                        'score': score
                    })
                    
                    if score < best_score:
                        best_score = score
                        best_params = {
                            'method': method,
                            'threshold_factor': threshold_factor,
                            'min_distance': min_distance,
                            'bcg_count': bcg_count,
                            'count_diff': count_diff,
                            'count_ratio': count_ratio,
                            'score': score
                        }
                    
                    if test_count % 10 == 0:
                        print(f"  Progress: {test_count}/{total_tests} ({test_count/total_tests*100:.1f}%)")
                        
                except Exception as e:
                    print(f"  Error with {method}, thr={threshold_factor}, dist={min_distance}: {e}")
                    continue
    
    # Sort and display results
    results.sort(key=lambda x: x['score'])
    
    print(f"\nTop 10 parameter combinations (ECG reference: {ecg_count} peaks):")
    print("Rank Method   Thr  Dist  BCG  Diff Ratio Score")
    print("-" * 55)
    
    for i, result in enumerate(results[:10]):
        print(f"{i+1:4} {result['method']:8} {result['threshold_factor']:4} {result['min_distance']:5} "
              f"{result['bcg_count']:4} {result['count_diff']:4} {result['count_ratio']:5.2f} {result['score']:5.1f}")
    
    if best_params:
        print(f"\n🏆 Best parameters:")
        print(f"   Method: {best_params['method']}")
        print(f"   Threshold factor: {best_params['threshold_factor']}")
        print(f"   Min distance: {best_params['min_distance']} seconds")
        print(f"   BCG peaks: {best_params['bcg_count']} (vs ECG: {ecg_count})")
        print(f"   Difference: {best_params['count_diff']} peaks")
        print(f"   Ratio: {best_params['count_ratio']:.2f}")
        print(f"   Score: {best_params['score']:.1f}")
        
        # Test the positive amplitude filtering effect
        print(f"\nTesting positive amplitude filtering effect...")
        
        # Test without positive filtering (modify function temporarily)
        bcg_peaks_all, _ = find_j_peaks_bcg(
            bcg_dwt, fs_bcg,
            method=best_params['method'],
            threshold_factor=best_params['threshold_factor'],
            min_distance_sec=best_params['min_distance']
        )
        
        # Count how many would be filtered out
        positive_count = 0
        negative_count = 0
        
        for peak_idx in bcg_peaks_all:
            if peak_idx < len(bcg_dwt):
                if bcg_dwt[peak_idx] > 0:
                    positive_count += 1
                else:
                    negative_count += 1
        
        print(f"   Total envelope peaks: {len(bcg_peaks_all)}")
        print(f"   Positive amplitude peaks: {positive_count}")
        print(f"   Negative amplitude peaks: {negative_count}")
        print(f"   Filtering effectiveness: {negative_count} peaks removed ({negative_count/len(bcg_peaks_all)*100:.1f}%)")
        
        return best_params
    else:
        print("No valid parameter combinations found!")
        return None

def create_visualization(best_params):
    """Create visualization with best parameters."""
    
    print(f"\nCreating visualization with best parameters...")
    
    # Load shorter data for visualization
    ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(
        '../data/lab4/AB4_ecg.txt', '../data/lab4/AB4_acc.txt', duration_minutes=1
    )
    fs_ecg = 1 / np.mean(np.diff(ecg_time))
    fs_bcg = 1 / np.mean(np.diff(bcg_time))
    
    # Preprocess
    ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
    bcg_filtered = preprocess_signal(bcg_signal, lowcut=0.5, highcut=20, fs=fs_bcg)
    
    ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet='db4', level=4)
    bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet='db4', level=4)
    
    min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
    min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))
    
    ecg_dwt = ecg_dwt[:min_len_ecg]
    bcg_dwt = bcg_dwt[:min_len_bcg]
    ecg_time_dwt = ecg_time[:min_len_ecg]
    bcg_time_dwt = bcg_time[:min_len_bcg]
    
    # Detect peaks
    ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
    bcg_peaks, bcg_envelope = find_j_peaks_bcg(
        bcg_dwt, fs_bcg,
        method=best_params['method'],
        threshold_factor=best_params['threshold_factor'],
        min_distance_sec=best_params['min_distance']
    )
    
    # Create plot
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    
    # ECG
    axes[0].plot(ecg_time_dwt, ecg_dwt, 'b-', alpha=0.7, label='ECG Signal')
    axes[0].plot(ecg_time_dwt[ecg_peaks], ecg_dwt[ecg_peaks], 'ro', markersize=6, label=f'R Peaks ({len(ecg_peaks)})')
    axes[0].set_title('ECG Signal with R Peaks')
    axes[0].set_ylabel('Amplitude (mV)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # BCG with positive/negative regions
    axes[1].plot(bcg_time_dwt, bcg_dwt, 'b-', alpha=0.7, label='BCG Signal')
    positive_mask = bcg_dwt > 0
    axes[1].fill_between(bcg_time_dwt, 0, bcg_dwt, where=positive_mask, alpha=0.3, color='green', label='Positive (J peaks)')
    axes[1].fill_between(bcg_time_dwt, 0, bcg_dwt, where=~positive_mask, alpha=0.3, color='red', label='Negative (filtered out)')
    axes[1].axhline(0, color='black', linestyle='-', alpha=0.5)
    axes[1].set_title('BCG Signal - Positive Amplitude Filtering')
    axes[1].set_ylabel('Magnitude (ADC units)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # Envelope with peaks
    axes[2].plot(bcg_time_dwt, bcg_envelope, 'g-', linewidth=2, label='Envelope')
    axes[2].plot(bcg_time_dwt[bcg_peaks], bcg_envelope[bcg_peaks], 'ro', markersize=8, label=f'J Peaks ({len(bcg_peaks)}) - Positive Only')
    
    # Threshold
    mu, sigma = bcg_envelope.mean(), bcg_envelope.std()
    threshold = mu + best_params['threshold_factor'] * sigma
    axes[2].axhline(threshold, color='orange', linestyle='--', alpha=0.8, label=f'Threshold (μ + {best_params["threshold_factor"]}σ)')
    
    axes[2].set_title(f'Optimized J Peak Detection\nMethod: {best_params["method"]}, Threshold: {best_params["threshold_factor"]}, Min Distance: {best_params["min_distance"]}s')
    axes[2].set_xlabel('Time (s)')
    axes[2].set_ylabel('Envelope Amplitude')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('optimized_j_peak_detection.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("Visualization saved as 'optimized_j_peak_detection.png'")

if __name__ == "__main__":
    best_params = test_j_peak_optimization()
    
    if best_params:
        create_visualization(best_params)
        
        print(f"\n=== Optimization Complete ===")
        print(f"Recommended function call:")
        print(f"bcg_peaks, bcg_envelope = find_j_peaks_bcg(")
        print(f"    bcg_signal, fs,")
        print(f"    method='{best_params['method']}',")
        print(f"    threshold_factor={best_params['threshold_factor']},")
        print(f"    min_distance_sec={best_params['min_distance']}")
        print(f")")
    else:
        print("Optimization failed!")
