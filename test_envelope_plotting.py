#!/usr/bin/env python3
"""
Test script to verify the envelope-based plotting functions work correctly.
"""

import numpy as np
import matplotlib.pyplot as plt
from hw4_utils import *

def test_envelope_plotting():
    """Test the envelope-based plotting functions."""
    
    print("=== Testing Envelope-based Plotting Functions ===\n")
    
    # Load a small amount of data for testing
    print("1. Loading test data...")
    try:
        ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(
            '../data/lab4/AB4_ecg.txt', '../data/lab4/AB4_acc.txt', duration_minutes=1
        )
        fs_ecg = 1 / np.mean(np.diff(ecg_time))
        fs_bcg = 1 / np.mean(np.diff(bcg_time))
        print(f"   ✓ Data loaded: ECG {fs_ecg:.1f} Hz, BCG {fs_bcg:.1f} Hz")
    except Exception as e:
        print(f"   ✗ Error loading data: {e}")
        return False
    
    # Preprocess signals
    print("\n2. Preprocessing signals...")
    try:
        ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
        bcg_filtered = preprocess_signal(bcg_signal, lowcut=0.5, highcut=20, fs=fs_bcg)
        
        ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet='db4', level=4)
        bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet='db4', level=4)
        
        # Ensure same length
        min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
        min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))
        
        ecg_dwt = ecg_dwt[:min_len_ecg]
        bcg_dwt = bcg_dwt[:min_len_bcg]
        ecg_time_dwt = ecg_time[:min_len_ecg]
        bcg_time_dwt = bcg_time[:min_len_bcg]
        
        print(f"   ✓ Preprocessing completed")
    except Exception as e:
        print(f"   ✗ Error in preprocessing: {e}")
        return False
    
    # Test peak detection with envelope
    print("\n3. Testing envelope-based peak detection...")
    try:
        # ECG peaks (for comparison)
        ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
        print(f"   ✓ ECG R peaks: {len(ecg_peaks)} detected")
        
        # BCG peaks with envelope
        bcg_peaks, bcg_envelope = find_j_peaks_bcg(bcg_dwt, fs_bcg, method='hilbert')
        print(f"   ✓ BCG J peaks: {len(bcg_peaks)} detected")
        print(f"   ✓ BCG envelope calculated: {len(bcg_envelope)} points")
        
        # Verify envelope is correct length
        assert len(bcg_envelope) == len(bcg_dwt), f"Envelope length mismatch: {len(bcg_envelope)} vs {len(bcg_dwt)}"
        print(f"   ✓ Envelope length verified")
        
    except Exception as e:
        print(f"   ✗ Error in peak detection: {e}")
        return False
    
    # Test plotting functions
    print("\n4. Testing plotting functions...")
    try:
        # Test 1: Original plot_peaks_detection (for ECG)
        print("   Testing ECG peak detection plot...")
        fig1 = plot_peaks_detection(
            ecg_time_dwt[:1000], ecg_dwt[:1000], 
            ecg_peaks[ecg_peaks < 1000],
            'Test ECG Peak Detection', 'Amplitude (mV)'
        )
        plt.close(fig1)
        print("   ✓ ECG peak detection plot works")
        
        # Test 2: New envelope-based plotting
        print("   Testing BCG envelope-based peak detection plot...")
        fig2 = plot_envelope_peaks_detection(
            bcg_time_dwt[:1000], bcg_dwt[:1000], bcg_envelope[:1000],
            bcg_peaks[bcg_peaks < 1000],
            'Test BCG Envelope Peak Detection', 'Magnitude (ADC units)'
        )
        plt.close(fig2)
        print("   ✓ BCG envelope peak detection plot works")
        
        # Test 3: BCG envelope detection function
        print("   Testing BCG envelope detection plot...")
        fig3 = plot_bcg_envelope_detection(
            bcg_time_dwt[:1000], bcg_dwt[:1000], bcg_envelope[:1000],
            bcg_peaks[bcg_peaks < 1000],
            'Test BCG Envelope Detection'
        )
        plt.close(fig3)
        print("   ✓ BCG envelope detection plot works")
        
    except Exception as e:
        print(f"   ✗ Error in plotting functions: {e}")
        return False
    
    # Test with actual visualization (save to file)
    print("\n5. Creating test visualization...")
    try:
        # Create a comprehensive test plot
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        
        # Plot 1: ECG with peaks
        time_window = 1000
        ecg_peaks_window = ecg_peaks[ecg_peaks < time_window]
        
        axes[0].plot(ecg_time_dwt[:time_window], ecg_dwt[:time_window], 'b-', label='ECG Signal')
        axes[0].plot(ecg_time_dwt[ecg_peaks_window], ecg_dwt[ecg_peaks_window], 'ro', markersize=8, label='R Peaks')
        axes[0].set_title('ECG Signal with R Peaks')
        axes[0].set_ylabel('Amplitude (mV)')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Plot 2: BCG signal without peaks
        bcg_peaks_window = bcg_peaks[bcg_peaks < time_window]
        
        axes[1].plot(bcg_time_dwt[:time_window], bcg_dwt[:time_window], 'b-', alpha=0.7, label='BCG Signal')
        axes[1].set_title('BCG Signal (Original - No Peaks Shown)')
        axes[1].set_ylabel('Magnitude (ADC units)')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # Plot 3: BCG envelope with peaks
        axes[2].plot(bcg_time_dwt[:time_window], bcg_envelope[:time_window], 'g-', linewidth=2, label='Signal Envelope')
        axes[2].plot(bcg_time_dwt[bcg_peaks_window], bcg_envelope[bcg_peaks_window], 'ro', markersize=8, label='J Peaks on Envelope')
        
        # Add threshold line
        envelope_mean = np.mean(bcg_envelope[:time_window])
        envelope_std = np.std(bcg_envelope[:time_window])
        threshold = envelope_mean + 1.5 * envelope_std
        axes[2].axhline(threshold, color='orange', linestyle='--', alpha=0.8, label=f'Threshold: {threshold:.2f}')
        
        axes[2].set_title('BCG Envelope with Peak Detection')
        axes[2].set_xlabel('Time (s)')
        axes[2].set_ylabel('Envelope Amplitude')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('envelope_plotting_test.png', dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        print("   ✓ Test visualization saved as 'envelope_plotting_test.png'")
        
    except Exception as e:
        print(f"   ✗ Error creating test visualization: {e}")
        return False
    
    print("\n=== All Envelope Plotting Tests Passed! ===")
    print("\nKey improvements verified:")
    print("✓ Peaks removed from BCG signal plots")
    print("✓ Peaks now shown only on calculated envelope")
    print("✓ New plot_envelope_peaks_detection() function works")
    print("✓ Updated plot_bcg_envelope_detection() function works")
    print("✓ Threshold visualization on envelope")
    print("\nThe notebook should now correctly show:")
    print("- ECG signal with R peaks marked")
    print("- BCG signal without peaks (clean view)")
    print("- BCG envelope with J peaks marked on envelope")
    
    return True

if __name__ == "__main__":
    test_envelope_plotting()
