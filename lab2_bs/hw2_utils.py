import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from scipy.fft import fft, ifft, fftfreq
from scipy.signal import find_peaks, butter, filtfilt
import pywt
from PyEMD import EMD
from typing import Tuple, List, Dict, Optional
from scipy.signal import hilbert
import warnings

warnings.filterwarnings("ignore")


def load_ppg_fieldstudy_data(
    subject_id: str = "S1",
    duration: Optional[float] = None,
    start_time: float = 0,
    data_path: str = "../data/PPG_FieldStudy",
) -> Tuple[np.ndarray, float, Dict]:
    """
    Load PPG data from the PPG_FieldStudy dataset.
    """
    import zipfile
    import os

    subject_path = os.path.join(data_path, subject_id)

    # Load metadata
    metadata = {}

    # Load activity data
    activity_file = os.path.join(subject_path, f"{subject_id}_activity.csv")
    if os.path.exists(activity_file):
        activity_df = pd.read_csv(activity_file)
        # Parse activity data (format: activity_name, timestamp)
        activities = []
        for _, row in activity_df.iterrows():
            activity_name = row.iloc[0].strip().replace("# ", "")
            timestamp = row.iloc[1]
            if activity_name != "SUBJECT_ID":
                activities.append({"activity": activity_name, "timestamp": timestamp})
        metadata["activities"] = activities

    # Load subject info
    quest_file = os.path.join(subject_path, f"{subject_id}_quest.csv")
    if os.path.exists(quest_file):
        quest_df = pd.read_csv(quest_file)
        subject_info = {}
        for _, row in quest_df.iterrows():
            key = row.iloc[0].strip().replace("# ", "")
            value = row.iloc[1]
            if key != "SUBJECT_ID":
                subject_info[key.lower()] = value
        metadata["subject_info"] = subject_info

        e4_file = os.path.join(subject_path, f"{subject_id}_E4.zip")

        with zipfile.ZipFile(e4_file, "r") as z:
            # Read BVP data
            with z.open("BVP.csv") as f:
                lines = f.read().decode("utf-8").strip().split("\n")

            # First line is start timestamp, second line is sampling frequency (stated in PDF)
            start_timestamp = float(lines[0])
            sampling_freq = float(lines[1])

            # Rest are PPG values
            ppg_values = np.array([float(line) for line in lines[2:]])

            metadata["start_timestamp"] = start_timestamp
            metadata["data_source"] = "E4_BVP"

    # Apply time windowing
    if start_time > 0 or duration is not None:
        start_sample = int(start_time * sampling_freq)

        if duration is not None:
            end_sample = start_sample + int(duration * sampling_freq)
            end_sample = min(end_sample, len(ppg_values))
        else:
            end_sample = len(ppg_values)

        ppg_values = ppg_values[start_sample:end_sample]

    ppg_signal = ppg_values.astype(np.float64)

    metadata["sampling_freq"] = sampling_freq
    metadata["duration"] = len(ppg_signal) / sampling_freq
    metadata["num_samples"] = len(ppg_signal)

    return ppg_signal, sampling_freq, metadata


def load_ground_truth_data(
    subject_id: str = "S1",
    start_time: float = 0,
    duration: Optional[float] = None,
    data_path: str = "../data/PPG_FieldStudy",
) -> Dict:
    """
    Load ground truth heart rate and respiration rate data from PPG_FieldStudy dataset.
    """
    import zipfile
    import os

    subject_path = os.path.join(data_path, subject_id)
    ground_truth = {}

    # Load ground truth heart rate from E4 HR.csv
    e4_file = os.path.join(subject_path, f"{subject_id}_E4.zip")

    try:
        with zipfile.ZipFile(e4_file, "r") as z:
            if "HR.csv" in z.namelist():
                with z.open("HR.csv") as f:
                    lines = f.read().decode("utf-8").strip().split("\n")

                # First line is start timestamp, second line is sampling frequency
                hr_start_timestamp = float(lines[0])
                hr_sampling_freq = float(lines[1])

                # Rest are heart rate values in bpm
                hr_values = np.array(
                    [float(line) for line in lines[2:] if line.strip()]
                )

                # Create time axis for HR data
                hr_time_axis = np.arange(len(hr_values)) / hr_sampling_freq

                # Apply time windowing
                if start_time > 0 or duration is not None:
                    start_idx = int(start_time * hr_sampling_freq)

                    if duration is not None:
                        end_idx = start_idx + int(duration * hr_sampling_freq)
                        end_idx = min(end_idx, len(hr_values))
                    else:
                        end_idx = len(hr_values)

                    hr_values = hr_values[start_idx:end_idx]
                    hr_time_axis = hr_time_axis[start_idx:end_idx]

                ground_truth["heart_rate"] = {
                    "values": hr_values,
                    "time_axis": hr_time_axis,
                    "sampling_freq": hr_sampling_freq,
                    "mean": np.mean(hr_values),
                    "std": np.std(hr_values),
                    "min": np.min(hr_values),
                    "max": np.max(hr_values),
                    "start_timestamp": hr_start_timestamp,
                }

    except Exception as e:
        print(f"Could not load ground truth heart rate: {e}")
        ground_truth["heart_rate"] = None

    return ground_truth


def compute_fft_spectrum(
    signal: np.ndarray, sampling_freq: float
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Compute FFT spectrum of the signal.
    """
    N = len(signal)
    fft_complex = fft(signal)
    frequencies = fftfreq(N, 1 / sampling_freq)
    magnitude = np.abs(fft_complex)

    return frequencies, magnitude, fft_complex


def design_bandpass_filter_fft(
    low_freq: float, high_freq: float, sampling_freq: float
) -> Tuple[float, float]:
    """
    Design parameters for FFT-based bandpass filtering.
    """
    nyquist = sampling_freq / 2
    low_norm = low_freq / nyquist
    high_norm = high_freq / nyquist
    return low_norm, high_norm


def apply_fft_bandpass_filter(
    signal: np.ndarray, low_freq: float, high_freq: float, sampling_freq: float
) -> np.ndarray:
    """
    Apply bandpass filter using FFT method.
    """
    # Compute FFT
    frequencies, magnitude, fft_complex = compute_fft_spectrum(signal, sampling_freq)

    # Create frequency mask
    freq_mask = (np.abs(frequencies) >= low_freq) & (np.abs(frequencies) <= high_freq)

    # Apply filter in frequency domain
    filtered_fft = fft_complex.copy()
    filtered_fft[~freq_mask] = 0

    # Inverse FFT
    filtered_signal = np.real(ifft(filtered_fft))

    return filtered_signal


def detect_heart_rate_from_peaks(
    signal: np.ndarray, sampling_freq: float, min_distance: float = 0.4
) -> Tuple[np.ndarray, float, List[float]]:
    """
    Detect heart rate from PPG signal peaks.
    """
    min_distance_samples = int(min_distance * sampling_freq)

    # Find peaks
    peaks, _ = find_peaks(signal, distance=min_distance_samples, height=np.mean(signal))

    # Ensure peaks is always a numpy array
    peaks = np.asarray(peaks)

    if len(peaks) < 2:
        return peaks, 0, []

    # Calculate instantaneous heart rates
    peak_intervals = np.diff(peaks) / sampling_freq
    instantaneous_hr = 60.0 / peak_intervals  # convert to bpm
    mean_hr = np.mean(instantaneous_hr)

    return peaks, mean_hr, instantaneous_hr.tolist()


def plot_fft_analysis(
    signal: np.ndarray,
    filtered_signal: np.ndarray,
    sampling_freq: float,
    title: str = "FFT-based PPG Analysis",
) -> None:
    """
    Plot original signal, filtered signal, and frequency spectra.
    """
    # Only plot first 10% of the data for clarity
    n_plot = int(0.1 * len(signal))
    time_axis = np.arange(n_plot) / sampling_freq

    # Compute FFT for both signals (full length)
    freqs_orig, mag_orig, _ = compute_fft_spectrum(signal, sampling_freq)
    freqs_filt, mag_filt, _ = compute_fft_spectrum(filtered_signal, sampling_freq)

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Original signal
    axes[0, 0].plot(time_axis, signal[:n_plot], "b-", alpha=0.7)
    axes[0, 0].set_title("Original PPG Signal")
    axes[0, 0].set_xlabel("Time (s)")
    axes[0, 0].set_ylabel("Amplitude")
    axes[0, 0].grid(True, alpha=0.3)

    # Filtered signal
    axes[0, 1].plot(time_axis, filtered_signal[:n_plot], "r-", alpha=0.8)
    axes[0, 1].set_title("Filtered PPG Signal")
    axes[0, 1].set_xlabel("Time (s)")
    axes[0, 1].set_ylabel("Amplitude")
    axes[0, 1].grid(True, alpha=0.3)

    # Original spectrum
    pos_mask_orig = freqs_orig >= 0
    axes[1, 0].plot(freqs_orig[pos_mask_orig], mag_orig[pos_mask_orig], "b-")
    axes[1, 0].set_title("Original Signal Spectrum")
    axes[1, 0].set_xlabel("Frequency (Hz)")
    axes[1, 0].set_ylabel("Magnitude")
    axes[1, 0].set_xlim(0, 5)
    axes[1, 0].grid(True, alpha=0.3)

    # Filtered spectrum
    pos_mask_filt = freqs_filt >= 0
    axes[1, 1].plot(freqs_filt[pos_mask_filt], mag_filt[pos_mask_filt], "r-")
    axes[1, 1].set_title("Filtered Signal Spectrum")
    axes[1, 1].set_xlabel("Frequency (Hz)")
    axes[1, 1].set_ylabel("Magnitude")
    axes[1, 1].set_xlim(0, 5)
    axes[1, 1].grid(True, alpha=0.3)

    plt.suptitle(title)
    plt.tight_layout()
    plt.show()


def wavelet_denoise_ppg(
    signal: np.ndarray,
    wavelet: str = "bior3.9",
    levels: int = 5,
    threshold_mode: str = "soft",
) -> Tuple[np.ndarray, List[np.ndarray], List[np.ndarray]]:
    """
    Denoise PPG signal using wavelet transform.
    """
    # Wavelet decomposition
    coeffs = pywt.wavedec(signal, wavelet, level=levels)
    coeffs_thresh = coeffs.copy()

    # Apply level-specific thresholding to detail coefficients
    for i in range(1, len(coeffs)):
        # Estimate noise level for this level
        sigma = np.median(np.abs(coeffs[i]))

        # I tried different thresholds empirically, this seemed to work best
        # Compute threshold for this level
        scaling_factor = 0.5
        threshold = sigma * np.sqrt(2 * np.log(len(coeffs[i]))) * scaling_factor

        # Apply thresholding
        threshold = 0.5 * min(threshold, np.max(np.abs(coeffs[i])))
        coeffs_thresh[i] = pywt.threshold(coeffs[i], threshold, mode=threshold_mode)

    # Reconstruct signal
    denoised_signal = pywt.waverec(coeffs_thresh, wavelet)

    return denoised_signal, coeffs, coeffs_thresh


def detect_respiration_from_ppg(
    signal: np.ndarray,
    sampling_freq: float,
    resp_freq_range: Tuple[float, float] = (0.1, 0.5),
) -> Tuple[np.ndarray, float, List[float]]:
    """
    Detect respiration rate from PPG signal envelope.
    """
    # Extract envelope using Hilbert transform
    analytic_signal = hilbert(signal)
    envelope = np.abs(analytic_signal)

    # Apply low-pass filter to extract respiratory component
    b, a = butter(4, resp_freq_range[1], btype="low", fs=sampling_freq)
    resp_component = filtfilt(b, a, envelope)

    # Find respiratory peaks
    min_distance_samples = int(1.0 / resp_freq_range[1] * sampling_freq)
    resp_peaks, _ = find_peaks(resp_component, distance=min_distance_samples)

    # Ensure resp_peaks is always a numpy array
    resp_peaks = np.asarray(resp_peaks)

    if len(resp_peaks) < 2:
        return resp_peaks, 0, []

    # Calculate respiration rates
    resp_intervals = np.diff(resp_peaks) / sampling_freq
    instantaneous_rr = 60.0 / resp_intervals  # breaths per minute
    mean_rr = np.mean(instantaneous_rr)

    return resp_peaks, mean_rr, instantaneous_rr.tolist()


def plot_wavelet_analysis(
    original_signal: np.ndarray,
    denoised_signal: np.ndarray,
    coeffs_original: List[np.ndarray],
    coeffs_thresholded: List[np.ndarray],
    sampling_freq: float,
    wavelet: str = "bior3.9",
) -> None:
    """
    Plot wavelet decomposition and denoising results.
    Only plot the first 10% of values for clarity.
    """
    # Only plot first 10% of the signal
    n_plot = int(0.1 * len(original_signal))
    time_axis = np.arange(n_plot) / sampling_freq

    fig, axes = plt.subplots(3, 2, figsize=(15, 12))

    # Original vs denoised signals
    axes[0, 0].plot(
        time_axis, original_signal[:n_plot], "r-", alpha=0.8, label="Original"
    )
    axes[0, 0].set_title("Original PPG Signal")
    axes[0, 0].set_xlabel("Time (s)")
    axes[0, 0].set_ylabel("Amplitude")
    axes[0, 0].grid(True, alpha=0.3)

    axes[0, 1].plot(
        time_axis, denoised_signal[:n_plot], "b-", alpha=0.8, label="Denoised"
    )
    axes[0, 1].set_title("Wavelet Denoised Signal")
    axes[0, 1].set_xlabel("Time (s)")
    axes[0, 1].set_ylabel("Amplitude")
    axes[0, 1].grid(True, alpha=0.3)

    # Plot some wavelet coefficients
    for i in range(min(4, len(coeffs_original) - 1)):
        if i < 2:
            row, col = 1, i
        else:
            row, col = 2, i - 2

        coeff_len = len(coeffs_original[i + 1])
        n_coeff_plot = int(0.3 * coeff_len)
        coeff_time = np.arange(n_coeff_plot) / sampling_freq * (2 ** (i + 1))
        axes[row, col].plot(
            coeff_time,
            coeffs_original[i + 1][:n_coeff_plot],
            "r-",
            alpha=0.8,
            label="Original",
        )
        axes[row, col].plot(
            coeff_time,
            coeffs_thresholded[i + 1][:n_coeff_plot],
            "b-",
            alpha=0.8,
            label="Thresholded",
        )
        axes[row, col].set_title(f"Detail Coefficients Level {i + 1}")
        axes[row, col].set_xlabel("Time (s)")
        axes[row, col].set_ylabel("Coefficient Value")
        axes[row, col].legend()
        axes[row, col].grid(True, alpha=0.3)

    plt.suptitle(f"Wavelet Analysis using {wavelet}")
    plt.tight_layout()
    plt.show()


def emd_decompose_ppg(
    signal: np.ndarray, method: str = "EMD", **kwargs
) -> Tuple[np.ndarray, List[np.ndarray]]:
    """
    Decompose PPG signal using EMD variants.
    """
    emd = EMD()
    imfs = emd.emd(signal)

    # Separate IMFs and residue
    if len(imfs) > 0:
        imfs_list = imfs[:-1] if len(imfs) > 1 else []
        residue = imfs[-1] if len(imfs) > 0 else signal
    else:
        imfs_list = []
        residue = signal

    return residue, imfs_list


def select_relevant_imfs(
    imfs: List[np.ndarray],
    sampling_freq: float,
    heart_rate_range: Tuple[float, float] = (0.7, 4.0),
    resp_rate_range: Tuple[float, float] = (0.1, 0.5),
) -> Tuple[List[int], List[int], List[int]]:
    """
    Select relevant IMFs based on frequency content.
    """
    heart_rate_imfs = []
    resp_rate_imfs = []
    noise_imfs = []

    for i, imf in enumerate(imfs):
        # Compute dominant frequency of IMF
        freqs, magnitude, _ = compute_fft_spectrum(imf, sampling_freq)
        pos_freqs = freqs[freqs >= 0]
        pos_magnitude = magnitude[freqs >= 0]

        if len(pos_magnitude) > 1:
            dominant_freq = pos_freqs[np.argmax(pos_magnitude[1:])]  # Skip DC component

            if heart_rate_range[0] <= dominant_freq <= heart_rate_range[1]:
                heart_rate_imfs.append(i)
            elif resp_rate_range[0] <= dominant_freq <= resp_rate_range[1]:
                resp_rate_imfs.append(i)
            else:
                noise_imfs.append(i)
        else:
            noise_imfs.append(i)

    return heart_rate_imfs, resp_rate_imfs, noise_imfs


def reconstruct_from_selected_imfs(
    imfs: List[np.ndarray],
    selected_indices: List[int],
    residue: Optional[np.ndarray] = None,
) -> np.ndarray:
    """
    Reconstruct signal from selected IMFs.
    """
    if not selected_indices:
        return np.zeros_like(imfs[0]) if imfs else np.array([])

    reconstructed = np.sum([imfs[i] for i in selected_indices], axis=0)

    if residue is not None:
        reconstructed += residue

    return reconstructed


def plot_emd_analysis(
    original_signal: np.ndarray,
    imfs: List[np.ndarray],
    residue: np.ndarray,
    sampling_freq: float,
    max_imfs_to_plot: int = 6,
) -> None:
    """
    Plot EMD decomposition results.
    """
    time_axis = np.arange(len(original_signal)) / sampling_freq

    n_plots = min(len(imfs), max_imfs_to_plot) + 2  # +2 for original and residue
    fig, axes = plt.subplots(n_plots, 1, figsize=(12, 2 * n_plots))

    if n_plots == 1:
        axes = [axes]

    # Original signal
    axes[0].plot(time_axis, original_signal, "b-")
    axes[0].set_title("Original PPG Signal")
    axes[0].set_ylabel("Amplitude")
    axes[0].grid(True, alpha=0.3)

    # IMFs
    for i in range(min(len(imfs), max_imfs_to_plot)):
        axes[i + 1].plot(time_axis, imfs[i], "g-")
        axes[i + 1].set_title(f"IMF {i + 1}")
        axes[i + 1].set_ylabel("Amplitude")
        axes[i + 1].grid(True, alpha=0.3)

    # Residue
    if len(axes) > len(imfs) + 1:
        axes[-1].plot(time_axis, residue, "r-")
        axes[-1].set_title("Residue")
        axes[-1].set_xlabel("Time (s)")
        axes[-1].set_ylabel("Amplitude")
        axes[-1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()


def comprehensive_ppg_analysis(
    signal: np.ndarray,
    sampling_freq: float,
    methods: List[str] = ["FFT", "Wavelet", "EMD"],
) -> Dict:
    """
    Perform comprehensive PPG analysis using multiple methods.
    """
    results = {"original_signal": signal, "sampling_freq": sampling_freq}

    # FFT-based analysis
    if "FFT" in methods:
        fft_filtered = apply_fft_bandpass_filter(signal, 0.7, 4.0, sampling_freq)
        fft_peaks, fft_hr, fft_hr_inst = detect_heart_rate_from_peaks(
            fft_filtered, sampling_freq
        )

        results["fft"] = {
            "filtered_signal": fft_filtered,
            "peaks": fft_peaks,
            "heart_rate": fft_hr,
            "instantaneous_hr": fft_hr_inst,
        }

    # Wavelet-based analysis
    if "Wavelet" in methods:
        wav_denoised, wav_coeffs_orig, wav_coeffs_thresh = wavelet_denoise_ppg(signal)
        wav_peaks, wav_hr, wav_hr_inst = detect_heart_rate_from_peaks(
            wav_denoised, sampling_freq
        )
        wav_resp_peaks, wav_rr, wav_rr_inst = detect_respiration_from_ppg(
            wav_denoised, sampling_freq
        )

        results["wavelet"] = {
            "denoised_signal": wav_denoised,
            "coeffs_original": wav_coeffs_orig,
            "coeffs_thresholded": wav_coeffs_thresh,
            "heart_peaks": wav_peaks,
            "heart_rate": wav_hr,
            "instantaneous_hr": wav_hr_inst,
            "resp_peaks": wav_resp_peaks,
            "resp_rate": wav_rr,
            "instantaneous_rr": wav_rr_inst,
        }

    # EMD-based analysis
    if "EMD" in methods:
        emd_residue, emd_imfs = emd_decompose_ppg(signal, method="EMD")
        hr_imfs, rr_imfs, noise_imfs = select_relevant_imfs(emd_imfs, sampling_freq)

        # Reconstruct signals
        emd_hr_signal = reconstruct_from_selected_imfs(emd_imfs, hr_imfs)
        emd_rr_signal = reconstruct_from_selected_imfs(emd_imfs, rr_imfs)

        # Detect peaks
        if len(emd_hr_signal) > 0:
            emd_hr_peaks, emd_hr_rate, emd_hr_inst = detect_heart_rate_from_peaks(
                emd_hr_signal, sampling_freq
            )
        else:
            emd_hr_peaks, emd_hr_rate, emd_hr_inst = np.array([]), 0, []

        if len(emd_rr_signal) > 0:
            emd_rr_peaks, emd_rr_rate, emd_rr_inst = detect_respiration_from_ppg(
                emd_rr_signal, sampling_freq
            )
        else:
            emd_rr_peaks, emd_rr_rate, emd_rr_inst = np.array([]), 0, []

        results["emd"] = {
            "imfs": emd_imfs,
            "residue": emd_residue,
            "heart_rate_imfs": hr_imfs,
            "resp_rate_imfs": rr_imfs,
            "noise_imfs": noise_imfs,
            "heart_rate_signal": emd_hr_signal,
            "resp_rate_signal": emd_rr_signal,
            "heart_peaks": emd_hr_peaks,
            "heart_rate": emd_hr_rate,
            "instantaneous_hr": emd_hr_inst,
            "resp_peaks": emd_rr_peaks,
            "resp_rate": emd_rr_rate,
            "instantaneous_rr": emd_rr_inst,
        }

    return results
