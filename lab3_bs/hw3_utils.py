import numpy as np
import pandas as pd
import pywt
import scipy.signal as signal
import matplotlib.pyplot as plt


def load_synchronized_data(ecg_file, fsr_file):
    """Load and synchronize ECG and FSR data from text files."""
    # Load ECG data
    ecg_data = pd.read_csv(ecg_file, sep=";")
    ecg_signal = ecg_data["ECG II"].values
    ecg_time = ecg_data["ctime"].values

    # Load FSR data (channel 2)
    fsr_data = pd.read_csv(fsr_file, sep=";")
    fsr_signal = fsr_data["v2"].values
    fsr_time = fsr_data["ctime"].values

    return ecg_signal, ecg_time, fsr_signal, fsr_time


def preprocess_signal(signal_data, lowcut, highcut, fs, order=4):
    """Apply bandpass filter to signal."""
    nyquist = 0.5 * fs
    low = lowcut / nyquist
    high = highcut / nyquist
    b, a = signal.butter(order, [low, high], btype="band")
    return signal.filtfilt(b, a, signal_data)


def apply_dwt_reconstruction(signal_data, wavelet="bior3.9", level=4):
    """Apply DWT decomposition and reconstruction."""
    coeffs = pywt.wavedec(signal_data, wavelet, level=level)
    reconstructed = pywt.waverec(coeffs, wavelet)
    return reconstructed


def find_r_peaks(ecg_signal, fs):
    """Find R peaks in ECG signal."""
    min_rr_ms = 300
    prom_pct = 90
    distance = int(fs * min_rr_ms / 1000)
    prominence = np.percentile(ecg_signal, prom_pct)

    peaks, _ = signal.find_peaks(ecg_signal, distance=distance, prominence=prominence)
    return peaks


def find_fsr_peaks(fsr_signal, fs):
    """Find peaks in FSR signal."""

    min_rr_ms = 50
    prom_pct = 70
    distance = int(fs * min_rr_ms / 1000)
    prominence = np.percentile(fsr_signal, prom_pct)

    peaks, _ = signal.find_peaks(fsr_signal, distance=distance, prominence=prominence)
    return peaks


def calculate_ptt(
    ecg_peaks,
    fs_ecg,
    fsr_peaks,
    fs_fsr,
    min_ptt_ms=80,
    max_ptt_ms=500,
    window_size=9,
    fsr_signal=None,
):
    """
    Calculate Pulse Transit Time (PTT) by matching ECG R-peaks to FSR peaks.
    I choose 80-350ms to mimic physiological senseful range.
    """
    # Convert peaks to times (s)
    ecg_times = np.array(ecg_peaks) / fs_ecg
    fsr_times = np.array(fsr_peaks) / fs_fsr
    # bounds in seconds
    min_ptt = min_ptt_ms / 1000
    max_ptt = max_ptt_ms / 1000

    ptt_list = []
    matched_ecg = []
    matched_fsr = []

    j = 0
    N_fsr = len(fsr_times)
    for i, t_ecg in enumerate(ecg_times):
        # advance pointer to first candidate exceeding min_ptt
        while j < N_fsr and fsr_times[j] < t_ecg + min_ptt:
            j += 1
        # collect fsr peaks within max_ptt window
        candidates = []
        k = j
        while k < N_fsr and fsr_times[k] <= t_ecg + max_ptt:
            candidates.append(k)
            k += 1

        if len(candidates) == 0:
            continue

        if fsr_signal is not None:
            # Take the candidate with the highest amplitude
            j_idx = candidates[np.argmax(fsr_signal[candidates])]
        else:
            # if no fsr_signal provided, just take the first candidate
            j_idx = candidates[0]

        delta_t = fsr_times[j_idx] - t_ecg
        if min_ptt <= delta_t <= max_ptt:
            ptt_list.append(delta_t * 1000)
            matched_ecg.append(ecg_peaks[i])
            matched_fsr.append(fsr_peaks[j_idx])

    # Moving average filtering and clipping the peaks
    ptt_list = np.convolve(ptt_list, np.ones(window_size) / window_size, mode="valid")
    matched_ecg = matched_ecg[(window_size - 1) // 2 : -(window_size - 1) // 2]
    matched_fsr = matched_fsr[(window_size - 1) // 2 : -(window_size - 1) // 2]

    return (
        np.array(ptt_list),
        np.array(matched_ecg, dtype=int),
        np.array(matched_fsr, dtype=int),
    )


def plot_ptt_zoom(
    ecg_time,
    ecg_signal,
    fsr_time,
    fsr_signal,
    matched_ecg_peaks,
    matched_fsr_peaks,
    start_time=None,
    end_time=None,
):
    """
    Plot ECG and FSR signals with matched peaks within a specified time window.
    """
    # Default to full range if window not set
    if start_time is None:
        start_time = min(ecg_time[0], fsr_time[0])
    if end_time is None:
        end_time = max(ecg_time[-1], fsr_time[-1])

    # Determine which peaks fall in the zoom window
    ecg_peaks_in_window = [
        p for p in matched_ecg_peaks if start_time <= ecg_time[p] <= end_time
    ]
    fsr_peaks_in_window = [
        p for p in matched_fsr_peaks if start_time <= fsr_time[p] <= end_time
    ]

    # Create figure and plot full signals
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)

    # ECG subplot: full signal
    ax1.plot(ecg_time, ecg_signal, label="ECG")
    # Highlight matched peaks within window
    ax1.plot(
        ecg_time[ecg_peaks_in_window],
        ecg_signal[ecg_peaks_in_window],
        "ro",
        markersize=8,
        label="Matched R Peaks",
    )
    ax1.set_ylabel("Amplitude")
    ax1.legend()
    ax1.grid(True)
    ax1.set_xlim(start_time, end_time)
    ax1.set_title(f"ECG Signal ({start_time:.1f} to {end_time:.1f} s)")

    # FSR subplot: full signal
    ax2.plot(fsr_time, fsr_signal, label="FSR")
    # Highlight matched peaks within window
    ax2.plot(
        fsr_time[fsr_peaks_in_window],
        fsr_signal[fsr_peaks_in_window],
        "mo",
        markersize=8,
        label="Matched FSR Peaks",
    )
    ax2.set_ylabel("Amplitude")
    ax2.set_xlabel("Time (s)")
    ax2.legend()
    ax2.grid(True)
    ax2.set_xlim(start_time, end_time)
    ax2.set_title(f"FSR Signal ({start_time:.1f} to {end_time:.1f} s)")

    plt.tight_layout()
    return fig


def estimate_bp_linear(ptt_ms, sys_ref=121, pat_ref=269):
    """Estimate (systolic) blood pressure with literature-based linear model."""
    a_sys = -1.45
    b_sys = sys_ref - a_sys * pat_ref
    pat_ms = ptt_ms + 58.5
    systolic_bp = a_sys * pat_ms + b_sys

    return systolic_bp


def plot_signals(
    time1,
    signal1,
    time2,
    signal2,
    title1,
    title2,
    ylabel1,
    ylabel2,
    start_time=None,
    end_time=None,
):
    """Plot two signals in subplots."""
    # Only plot selected timeslot
    if start_time is not None and end_time is not None:
        mask1 = (time1 >= start_time) & (time1 <= end_time)
        mask2 = (time2 >= start_time) & (time2 <= end_time)
        time1 = time1[mask1]
        signal1 = signal1[mask1]
        time2 = time2[mask2]
        signal2 = signal2[mask2]

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

    ax1.plot(time1, signal1)
    ax1.set_title(title1)
    ax1.set_ylabel(ylabel1)
    ax1.grid(True)

    ax2.plot(time2, signal2)
    ax2.set_title(title2)
    ax2.set_xlabel("Time (s)")
    ax2.set_ylabel(ylabel2)
    ax2.grid(True)

    plt.tight_layout()
    return fig


def plot_peaks_detection(
    time, signal, peaks, title, ylabel, start_time=None, end_time=None
):
    """Plot a subset of the signal with detected peaks between start_time and end_time."""
    # Determine mask for the subset range
    if start_time is not None and end_time is not None:
        mask = (time >= start_time) & (time <= end_time)
    elif start_time is not None:
        mask = time >= start_time
    elif end_time is not None:
        mask = time <= end_time
    else:
        mask = np.ones_like(time, dtype=bool)

    # Apply mask to time and signal
    t_sub = time[mask]
    sig_sub = signal[mask]

    # Filter peaks to those within the subset window
    peak_mask = mask[peaks]
    peaks_sub = peaks[peak_mask]

    fig, ax = plt.subplots(figsize=(12, 6))
    ax.plot(t_sub, sig_sub, label="Signal")
    ax.plot(
        time[peaks_sub], signal[peaks_sub], "ro", markersize=8, label="Detected Peaks"
    )
    ax.set_title(title)
    ax.set_xlabel("Time (s)")
    ax.set_ylabel(ylabel)
    ax.legend()
    ax.grid(True)
    return fig
