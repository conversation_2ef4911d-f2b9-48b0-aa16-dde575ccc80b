#!/usr/bin/env python3
"""
Test script for HW4: ECG vs BCG Heart Rate and HRV Analysis
This script tests the main functions and provides a quick overview of the analysis.
"""

import numpy as np
import matplotlib.pyplot as plt
from hw4_utils import *


def test_hw4_analysis():
    """Test the HW4 analysis pipeline."""

    print("=== HW4 ECG vs BCG Analysis Test ===\n")

    # File paths
    ecg_file = "../data/lab4/AB4_ecg.txt"
    acc_file = "../data/lab4/AB4_acc.txt"

    print("1. Loading data...")
    try:
        ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(
            ecg_file, acc_file, duration_minutes=5
        )
        print(f"   ✓ ECG: {len(ecg_signal)} samples, {ecg_time[-1]:.1f}s duration")
        print(f"   ✓ BCG: {len(bcg_signal)} samples, {bcg_time[-1]:.1f}s duration")
    except Exception as e:
        print(f"   ✗ Error loading data: {e}")
        return

    # Calculate sampling frequencies
    fs_ecg = 1 / np.mean(np.diff(ecg_time))
    fs_bcg = 1 / np.mean(np.diff(bcg_time))
    print(f"   ✓ Sampling rates - ECG: {fs_ecg:.1f} Hz, BCG: {fs_bcg:.1f} Hz")

    print("\n2. Preprocessing signals...")
    try:
        # Bandpass filtering
        ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
        bcg_filtered = preprocess_signal(bcg_signal, lowcut=1, highcut=20, fs=fs_bcg)

        # DWT denoising
        ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet="db4", level=6)
        bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet="db6", level=8)

        # Ensure same length
        min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
        min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))

        ecg_dwt = ecg_dwt[:min_len_ecg]
        bcg_dwt = bcg_dwt[:min_len_bcg]
        ecg_time_dwt = ecg_time[:min_len_ecg]
        bcg_time_dwt = bcg_time[:min_len_bcg]

        print("   ✓ Bandpass filtering completed")
        print("   ✓ DWT denoising completed")
    except Exception as e:
        print(f"   ✗ Error in preprocessing: {e}")
        return

    print("\n3. Peak detection...")
    try:
        ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
        bcg_peaks, bcg_envelope = find_j_peaks_bcg(bcg_dwt, fs_bcg, method="hilbert")

        print(f"   ✓ ECG R peaks: {len(ecg_peaks)} detected")
        print(
            f"   ✓ BCG J peaks: {len(bcg_peaks)} detected (using improved envelope method)"
        )

        if len(ecg_peaks) > 1:
            avg_hr_ecg = (
                60 * fs_ecg * (len(ecg_peaks) - 1) / (ecg_peaks[-1] - ecg_peaks[0])
            )
            print(f"   ✓ Average HR from ECG: {avg_hr_ecg:.1f} bpm")

        if len(bcg_peaks) > 1:
            avg_hr_bcg = (
                60 * fs_bcg * (len(bcg_peaks) - 1) / (bcg_peaks[-1] - bcg_peaks[0])
            )
            print(f"   ✓ Average HR from BCG: {avg_hr_bcg:.1f} bpm")

    except Exception as e:
        print(f"   ✗ Error in peak detection: {e}")
        return

    print("\n4. RR interval analysis...")
    try:
        rr_ecg = calculate_rr_intervals(ecg_peaks, fs_ecg)
        rr_bcg = calculate_rr_intervals(bcg_peaks, fs_bcg)

        print(f"   ✓ ECG RR intervals: {len(rr_ecg)} intervals")
        print(f"   ✓ BCG RR intervals: {len(rr_bcg)} intervals")

        if len(rr_ecg) > 0:
            print(
                f"   ✓ ECG RR - Mean: {np.mean(rr_ecg):.1f} ms, Std: {np.std(rr_ecg):.1f} ms"
            )
        if len(rr_bcg) > 0:
            print(
                f"   ✓ BCG RR - Mean: {np.mean(rr_bcg):.1f} ms, Std: {np.std(rr_bcg):.1f} ms"
            )

    except Exception as e:
        print(f"   ✗ Error in RR interval calculation: {e}")
        return

    print("\n5. HRV analysis...")
    try:
        if len(rr_ecg) > 1:
            hrv_ecg = calculate_hrv_metrics(rr_ecg)
            print(f"   ✓ ECG HRV metrics calculated:")
            print(f"     - RMSSD: {hrv_ecg['rmssd']:.2f} ms")
            print(f"     - pNN50: {hrv_ecg['pnn50']:.2f} %")

        if len(rr_bcg) > 1:
            hrv_bcg = calculate_hrv_metrics(rr_bcg)
            print(f"   ✓ BCG HRV metrics calculated:")
            print(f"     - RMSSD: {hrv_bcg['rmssd']:.2f} ms")
            print(f"     - pNN50: {hrv_bcg['pnn50']:.2f} %")

    except Exception as e:
        print(f"   ✗ Error in HRV analysis: {e}")
        return

    print("\n6. Frequency domain analysis...")
    try:
        if len(rr_ecg) > 10:
            rr_times_ecg = np.cumsum(np.concatenate([[0], rr_ecg[:-1]])) / 1000
            freq_hrv_ecg, freqs_ecg, psd_ecg = calculate_frequency_domain_hrv(
                rr_ecg, rr_times_ecg
            )
            print(f"   ✓ ECG frequency domain HRV:")
            print(f"     - LF power: {freq_hrv_ecg['lf_power']:.4f} ms²")
            print(f"     - HF power: {freq_hrv_ecg['hf_power']:.4f} ms²")
            print(f"     - LF/HF ratio: {freq_hrv_ecg['lf_hf_ratio']:.2f}")

        if len(rr_bcg) > 10:
            rr_times_bcg = np.cumsum(np.concatenate([[0], rr_bcg[:-1]])) / 1000
            freq_hrv_bcg, freqs_bcg, psd_bcg = calculate_frequency_domain_hrv(
                rr_bcg, rr_times_bcg
            )
            print(f"   ✓ BCG frequency domain HRV:")
            print(f"     - LF power: {freq_hrv_bcg['lf_power']:.4f} ms²")
            print(f"     - HF power: {freq_hrv_bcg['hf_power']:.4f} ms²")
            print(f"     - LF/HF ratio: {freq_hrv_bcg['lf_hf_ratio']:.2f}")

    except Exception as e:
        print(f"   ✗ Error in frequency domain analysis: {e}")
        return

    print("\n7. Creating summary visualization...")
    try:
        # Create a summary plot
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # Plot 1: Raw signals (first 30 seconds)
        mask_30s_ecg = ecg_time <= 30
        mask_30s_bcg = bcg_time <= 30

        axes[0, 0].plot(
            ecg_time[mask_30s_ecg], ecg_signal[mask_30s_ecg], "r-", alpha=0.7
        )
        axes[0, 0].set_title("ECG Signal (First 30s)")
        axes[0, 0].set_ylabel("Amplitude (mV)")
        axes[0, 0].grid(True, alpha=0.3)

        axes[0, 1].plot(
            bcg_time[mask_30s_bcg], bcg_signal[mask_30s_bcg], "b-", alpha=0.7
        )
        axes[0, 1].set_title("BCG Signal (First 30s)")
        axes[0, 1].set_ylabel("Magnitude (ADC units)")
        axes[0, 1].grid(True, alpha=0.3)

        # Plot 2: RR interval comparison
        if len(rr_ecg) > 0 and len(rr_bcg) > 0:
            axes[1, 0].hist(rr_ecg, bins=20, alpha=0.7, color="red", label="ECG")
            axes[1, 0].hist(rr_bcg, bins=20, alpha=0.7, color="blue", label="BCG")
            axes[1, 0].set_title("RR Interval Distribution")
            axes[1, 0].set_xlabel("RR Interval (ms)")
            axes[1, 0].set_ylabel("Frequency")
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

        # Plot 3: Heart rate comparison (with smoothing demonstration)
        if len(rr_ecg) > 0 and len(rr_bcg) > 0:
            hr_ecg = calculate_heart_rate(rr_ecg)
            hr_bcg = calculate_heart_rate(rr_bcg)
            hr_bcg_smoothed = calculate_heart_rate(rr_bcg, smooth=True)

            if len(ecg_peaks) > 1 and len(bcg_peaks) > 1:
                hr_times_ecg = ecg_time_dwt[ecg_peaks[1:]]
                hr_times_bcg = bcg_time_dwt[bcg_peaks[1:]]

                axes[1, 1].plot(hr_times_ecg, hr_ecg, "r-", alpha=0.8, label="ECG HR")
                axes[1, 1].plot(
                    hr_times_bcg,
                    hr_bcg,
                    "b-",
                    alpha=0.6,
                    linewidth=1,
                    label="BCG HR (Original)",
                )
                axes[1, 1].plot(
                    hr_times_bcg,
                    hr_bcg_smoothed,
                    "g-",
                    linewidth=2,
                    label="BCG HR (Smoothed)",
                )
                axes[1, 1].set_title("Heart Rate Comparison (with 3-beat smoothing)")
                axes[1, 1].set_xlabel("Time (s)")
                axes[1, 1].set_ylabel("Heart Rate (bpm)")
                axes[1, 1].legend()
                axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig("hw4_test_summary.png", dpi=150, bbox_inches="tight")
        plt.show()

        print("   ✓ Summary visualization created and saved as 'hw4_test_summary.png'")

    except Exception as e:
        print(f"   ✗ Error creating visualization: {e}")

    print("\n=== Test completed successfully! ===")
    print("\nNext steps:")
    print("1. Run the full Jupyter notebook: hw4.ipynb")
    print("2. Examine detailed analysis and visualizations")
    print("3. Compare ECG and BCG results in detail")

    return True


if __name__ == "__main__":
    test_hw4_analysis()
