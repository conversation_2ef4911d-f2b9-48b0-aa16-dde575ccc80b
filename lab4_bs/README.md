# HW4: Heart Rate and Heart Rate Variability Analysis - ECG vs BCG

## Overview

This project analyzes heart rate (HR) and heart rate variability (HRV) from synchronized ECG and accelerometer-based BCG (Ballistocardiogram) signals. The analysis focuses on comparing the two modalities for cardiac assessment using the first 5 minutes of recorded data.

## Dataset

- **ECG Data**: `AB4_ecg.txt` - Standard ECG Lead II recordings
- **Accelerometer Data**: `AB4_acc.txt` - 3-axis accelerometer data from 3 sensor sets
- **Analysis Duration**: First 5 minutes of synchronized recordings
- **BCG Signal**: 3D magnitude calculated from Set 2 accelerometer data (X2, Y2, Z2)

## Files Structure

```
lab4_bs/
├── hw4.ipynb           # Main Jupyter notebook with complete analysis
├── hw4_utils.py        # Utility functions for signal processing and analysis
├── test_hw4.py         # Test script to verify implementation
└── README.md           # This documentation file
```

## Key Features

### 1. **Signal Processing Pipeline**
- **Data Loading**: Synchronized ECG and accelerometer data loading
- **3D Magnitude Calculation**: Compute magnitude from X2, Y2, Z2 accelerometer channels
- **Bandpass Filtering**: ECG (0.5-40 Hz), BCG (1-20 Hz)
- **DWT Denoising**: Discrete Wavelet Transform for noise reduction

### 2. **Improved Peak Detection**
- **ECG R Peaks**: Robust R peak detection with adaptive thresholding
- **BCG J Peaks**: Enhanced ballistocardiogram J peak detection with:
  1. **Envelope Formation**: Hilbert transform or Rectify + Lowpass (2 Hz)
  2. **Adaptive Peak Detection**: MinDistance 0.4s with adaptive threshold
  3. **Quality Assessment**: Comparison between envelope methods

### 3. **Heart Rate Analysis with Smoothing**
- **RR Intervals**: Calculation from detected peaks
- **Instantaneous HR**: Beat-to-beat heart rate computation
- **3-Beat Moving Average**: HR curve smoothing for noise reduction
- **Statistical Comparison**: ECG vs BCG heart rate metrics
- **Method Comparison**: Hilbert vs Rectify+Lowpass envelope methods

### 4. **Heart Rate Variability (HRV)**

#### Time Domain Metrics:
- **Mean RR**: Average RR interval
- **RMSSD**: Root mean square of successive differences
- **pNN50**: Percentage of successive RR intervals differing by >50ms
- **Standard Deviation**: RR interval variability
- **Coefficient of Variation**: Normalized variability measure

#### Frequency Domain Metrics:
- **VLF Power**: Very Low Frequency (0.0033-0.04 Hz)
- **LF Power**: Low Frequency (0.04-0.15 Hz)
- **HF Power**: High Frequency (0.15-0.4 Hz)
- **LF/HF Ratio**: Autonomic balance indicator
- **Normalized Powers**: LF and HF in normalized units

### 5. **Comparative Analysis**
- **Correlation Analysis**: ECG vs BCG measurements
- **Bland-Altman Plots**: Agreement assessment
- **Statistical Comparison**: Comprehensive metric comparison
- **Poincaré Plots**: RR interval pattern visualization

## Usage

### Quick Test
```bash
cd lab4_bs
python test_hw4.py
```

### Full Analysis
Open and run the Jupyter notebook:
```bash
jupyter notebook hw4.ipynb
```

### Improved BCG Processing Algorithm

The enhanced BCG peak detection implements a three-step improvement process:

### Step 1: Envelope Formation
Two methods are available for creating the signal envelope:

**Hilbert Transform Method:**
```python
analytic_signal = signal.hilbert(bcg_signal)
envelope = np.abs(analytic_signal)
```

**Rectify + Lowpass Method:**
```python
rectified = np.abs(bcg_signal)
# Lowpass filter at 2 Hz
envelope = signal.filtfilt(b, a, rectified)
```

### Step 2: Peak Detection
- **Minimum Distance**: 0.4 seconds between peaks (prevents double detection)
- **Adaptive Threshold**: `mean + 1.5 * std` of envelope signal
- **Prominence Filtering**: Ensures peaks are sufficiently prominent

### Step 3: HR Curve Smoothing
- **3-Beat Moving Average**: Reduces noise in heart rate time series
- **Edge Preservation**: Maintains signal length with appropriate padding
- **Variance Reduction**: Typically reduces HR variability by 20-40%

## Key Functions in `hw4_utils.py`

```python
# Data loading
ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(ecg_file, acc_file, duration_minutes=5)

# Signal processing
filtered_signal = preprocess_signal(signal, lowcut, highcut, fs)
denoised_signal = apply_dwt_reconstruction(signal, wavelet='db4', level=6)

# Improved peak detection
ecg_peaks = find_r_peaks_ecg(ecg_signal, fs)
bcg_peaks, bcg_envelope = find_j_peaks_bcg(bcg_signal, fs, method='hilbert')

# HR and HRV analysis with smoothing
rr_intervals = calculate_rr_intervals(peaks, fs)
hr = calculate_heart_rate(rr_intervals)
hr_smoothed = calculate_heart_rate(rr_intervals, smooth=True)  # 3-beat moving average
hrv_metrics = calculate_hrv_metrics(rr_intervals)
freq_metrics, freqs, psd = calculate_frequency_domain_hrv(rr_intervals)

# HR curve smoothing
smoothed_hr = smooth_hr_curve(hr_values, window_size=3)
```

## Analysis Workflow

### 1. **Data Preprocessing** (Sections 1-6)
- Load synchronized ECG and accelerometer data
- Calculate 3D magnitude from accelerometer Set 2
- Apply bandpass filtering and DWT denoising
- Visualize raw and processed signals

### 2. **Peak Detection** (Sections 7-8)
- Detect R peaks in ECG and J peaks in BCG
- Visualize detected peaks with zoomed views
- Validate peak detection accuracy

### 3. **Heart Rate Analysis** (Sections 9-10)
- Calculate RR intervals and instantaneous heart rate
- Compare ECG and BCG heart rate trends
- Compute correlation between modalities

### 4. **HRV Analysis** (Sections 11-13)
- Time domain HRV metrics calculation
- Frequency domain analysis with PSD
- RR interval time series and Poincaré plots

### 5. **Comprehensive Comparison** (Sections 14-15)
- Statistical comparison of all metrics
- Bland-Altman agreement analysis
- Clinical interpretation and discussion

## Expected Results

### Signal Quality
- **ECG**: Clean, well-defined R peaks with consistent morphology
- **BCG**: More variable signal with motion artifacts, requiring robust processing

### Heart Rate Accuracy
- **Correlation**: Moderate to high correlation between ECG and BCG HR (r > 0.7)
- **Bias**: Small systematic differences due to detection algorithm variations
- **Precision**: ECG generally more precise, BCG shows higher variability

### HRV Metrics
- **Time Domain**: ECG provides more stable measurements
- **Frequency Domain**: Both modalities capture similar HRV patterns
- **Clinical Relevance**: BCG HRV metrics correlate with ECG but may include additional mechanical information

## Clinical Applications

### Advantages of BCG
1. **Non-invasive**: No skin contact required
2. **Continuous Monitoring**: Suitable for long-term assessment
3. **Unobtrusive**: Can be integrated into everyday objects (beds, chairs)
4. **Additional Information**: Provides cardiac mechanical activity

### Limitations
1. **Motion Sensitivity**: Susceptible to movement artifacts
2. **Signal Quality**: Lower SNR compared to ECG
3. **Positioning Dependency**: Requires proper sensor placement
4. **Individual Variation**: May need personalized calibration

## Technical Specifications

### Signal Processing
- **ECG Filtering**: 0.5-40 Hz bandpass (4th order Butterworth)
- **BCG Filtering**: 1-20 Hz bandpass (4th order Butterworth)
- **DWT**: Daubechies wavelets with soft thresholding
- **Peak Detection**: Adaptive thresholding with distance constraints

### HRV Analysis
- **Interpolation**: 4 Hz for frequency domain analysis
- **Window Function**: Hann window for spectral analysis
- **PSD Method**: Welch's method with overlapping segments
- **Frequency Bands**: Standard HRV frequency definitions

## Future Enhancements

1. **Advanced Filtering**: Adaptive noise cancellation
2. **Machine Learning**: AI-based peak detection and classification
3. **Real-time Processing**: Online analysis capabilities
4. **Multi-modal Fusion**: Combined ECG-BCG analysis
5. **Clinical Validation**: Large-scale validation studies

## Dependencies

- `numpy`: Numerical computations
- `pandas`: Data handling
- `scipy`: Signal processing and statistics
- `matplotlib`: Visualization
- `pywt`: Wavelet transforms

## References

- Heart Rate Variability Standards (Task Force, 1996)
- Ballistocardiography principles and applications
- Signal processing techniques for biomedical signals
