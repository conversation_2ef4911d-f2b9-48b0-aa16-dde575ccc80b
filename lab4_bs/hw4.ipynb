import numpy as np
import matplotlib.pyplot as plt
from hw4_utils import *

# Set matplotlib parameters for better plots
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

# Suppress warnings for cleaner output
import warnings
warnings.filterwarnings('ignore')

# File paths
ecg_file = '../data/lab4/AB4_ecg.txt'
acc_file = '../data/lab4/AB4_acc.txt'

# Load data (first 5 minutes)
ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(ecg_file, acc_file, duration_minutes=5)

print(f"ECG signal length: {len(ecg_signal)} samples")
print(f"BCG signal length: {len(bcg_signal)} samples")
print(f"ECG time range: {ecg_time[0]:.2f} - {ecg_time[-1]:.2f} seconds")
print(f"BCG time range: {bcg_time[0]:.2f} - {bcg_time[-1]:.2f} seconds")

# Calculate sampling frequencies
fs_ecg = 1 / np.mean(np.diff(ecg_time))
fs_bcg = 1 / np.mean(np.diff(bcg_time))

print(f"\nECG sampling frequency: {fs_ecg:.1f} Hz")
print(f"BCG sampling frequency: {fs_bcg:.1f} Hz")

# Plot raw signals
fig = plot_signals(
    ecg_time, ecg_signal, 
    bcg_time, bcg_signal,
    'Raw ECG Signal', 'Raw BCG Signal (3D Magnitude)',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Show a zoomed view of the first 30 seconds
zoom_mask_ecg = ecg_time <= 30
zoom_mask_bcg = bcg_time <= 30

fig = plot_signals(
    ecg_time[zoom_mask_ecg], ecg_signal[zoom_mask_ecg], 
    bcg_time[zoom_mask_bcg], bcg_signal[zoom_mask_bcg],
    'Raw ECG Signal (First 30s)', 'Raw BCG Signal (First 30s)',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Apply bandpass filtering
print("Applying bandpass filtering...")
ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
bcg_filtered = preprocess_signal(bcg_signal, lowcut=2, highcut=20, fs=fs_bcg)

print("Bandpass filtering completed.")

# Plot filtered signals
fig = plot_signals(
    ecg_time, ecg_filtered, 
    bcg_time, bcg_filtered,
    'Filtered ECG Signal (0.5-40 Hz)', 'Filtered BCG Signal (1-20 Hz)',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Show zoomed view
fig = plot_signals(
    ecg_time[zoom_mask_ecg], ecg_filtered[zoom_mask_ecg], 
    bcg_time[zoom_mask_bcg], bcg_filtered[zoom_mask_bcg],
    'Filtered ECG Signal (First 30s)', 'Filtered BCG Signal (First 30s)',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Apply DWT denoising
print("Applying DWT denoising...")
ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet='db4', level=4)
bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet='db4', level=4)

# Ensure same length
min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))

ecg_dwt = ecg_dwt[:min_len_ecg]
bcg_dwt = bcg_dwt[:min_len_bcg]
ecg_time_dwt = ecg_time[:min_len_ecg]
bcg_time_dwt = bcg_time[:min_len_bcg]

print("DWT denoising completed.")

# Plot DWT processed signals
fig = plot_signals(
    ecg_time_dwt, ecg_dwt, 
    bcg_time_dwt, bcg_dwt,
    'DWT Processed ECG Signal', 'DWT Processed BCG Signal',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Show zoomed view
zoom_mask_ecg_dwt = ecg_time_dwt <= 30
zoom_mask_bcg_dwt = bcg_time_dwt <= 30

fig = plot_signals(
    ecg_time_dwt[zoom_mask_ecg_dwt], ecg_dwt[zoom_mask_ecg_dwt], 
    bcg_time_dwt[zoom_mask_bcg_dwt], bcg_dwt[zoom_mask_bcg_dwt],
    'DWT Processed ECG Signal (First 30s)', 'DWT Processed BCG Signal (First 30s)',
    'Amplitude (mV)', 'Magnitude (ADC units)'
)
plt.show()

# Find peaks using improved methods
print("Detecting peaks with improved algorithms...")
ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
bcg_peaks, bcg_envelope = find_j_peaks_bcg(bcg_dwt, fs_bcg)

print(f"Found {len(ecg_peaks)} R peaks in ECG")
print(f"Found {len(bcg_peaks)} J peaks in BCG (using Hilbert envelope method)")

# Calculate average heart rates
if len(ecg_peaks) > 1:
    avg_hr_ecg = 60 * fs_ecg * (len(ecg_peaks) - 1) / (ecg_peaks[-1] - ecg_peaks[0])
    print(f"Average HR from ECG: {avg_hr_ecg:.1f} bpm")

if len(bcg_peaks) > 1:
    avg_hr_bcg = 60 * fs_bcg * (len(bcg_peaks) - 1) / (bcg_peaks[-1] - bcg_peaks[0])
    print(f"Average HR from BCG (Hilbert): {avg_hr_bcg:.1f} bpm")

# Plot ECG with R peaks
fig1 = plot_peaks_detection(
    ecg_time_dwt, ecg_dwt, ecg_peaks,
    'ECG Signal with Detected R Peaks', 'Amplitude (mV)'
)
plt.show()

# Plot BCG with J peaks
fig2 = plot_peaks_detection(
    bcg_time_dwt, bcg_dwt, bcg_peaks,
    'BCG Signal with Detected J Peaks', 'Magnitude (ADC units)'
)
plt.show()

# Show zoomed view of peak detection
zoom_start, zoom_end = 60, 90  # 30-second window
zoom_mask_ecg = (ecg_time_dwt >= zoom_start) & (ecg_time_dwt <= zoom_end)
zoom_mask_bcg = (bcg_time_dwt >= zoom_start) & (bcg_time_dwt <= zoom_end)
zoom_peaks_ecg = ecg_peaks[(ecg_time_dwt[ecg_peaks] >= zoom_start) & (ecg_time_dwt[ecg_peaks] <= zoom_end)]
zoom_peaks_bcg = bcg_peaks[(bcg_time_dwt[bcg_peaks] >= zoom_start) & (bcg_time_dwt[bcg_peaks] <= zoom_end)]

# Adjust peak indices for zoomed data
zoom_start_idx_ecg = np.where(zoom_mask_ecg)[0][0]
zoom_start_idx_bcg = np.where(zoom_mask_bcg)[0][0]
zoom_peaks_ecg_adj = zoom_peaks_ecg - zoom_start_idx_ecg
zoom_peaks_bcg_adj = zoom_peaks_bcg - zoom_start_idx_bcg

fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

# ECG zoomed
ax1.plot(ecg_time_dwt[zoom_mask_ecg], ecg_dwt[zoom_mask_ecg], 'b-', label='ECG')
ax1.plot(ecg_time_dwt[zoom_peaks_ecg], ecg_dwt[zoom_peaks_ecg], 'ro', markersize=8, label='R Peaks')
ax1.set_title('ECG Signal with R Peaks (Zoomed: 60-90s)')
ax1.set_ylabel('Amplitude (mV)')
ax1.legend()
ax1.grid(True)

# BCG zoomed - show envelope with peaks
ax2.plot(bcg_time_dwt[zoom_mask_bcg], bcg_dwt[zoom_mask_bcg], 'b-', alpha=0.6, label='BCG Signal')
ax2.plot(bcg_time_dwt[zoom_mask_bcg], bcg_envelope[zoom_mask_bcg], 'g-', linewidth=2, label='Envelope')
ax2.plot(bcg_time_dwt[zoom_peaks_bcg], bcg_envelope[zoom_peaks_bcg], 'ro', markersize=8, label='J Peaks on Envelope')
ax2.set_title('BCG Signal with Envelope and J Peaks (Zoomed: 60-90s)')
ax2.set_xlabel('Time (s)')
ax2.set_ylabel('Magnitude (ADC units)')
ax2.legend()
ax2.grid(True)

plt.tight_layout()
plt.show()