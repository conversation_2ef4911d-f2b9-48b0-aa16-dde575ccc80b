import numpy as np
import pandas as pd
import scipy.signal as signal
import matplotlib.pyplot as plt
from scipy import interpolate
from scipy.ndimage import median_filter
from scipy.interpolate import interp1d
import pywt


def load_bcg_ecg_data(ecg_file, acc_file, duration_minutes=5):
    """Load and synchronize ECG and accelerometer (BCG) data from text files."""
    # Load ECG data
    ecg_data = pd.read_csv(ecg_file, sep=";")
    ecg_signal = ecg_data["ECG II"].values
    ecg_time = ecg_data["ctime"].values

    # Load accelerometer data (using Set 2: X2, Y2, Z2)
    acc_data = pd.read_csv(acc_file, sep=";")
    acc_x = acc_data["x2"].values
    acc_y = acc_data["y2"].values
    acc_z = acc_data["z2"].values
    acc_time = acc_data["ctime"].values

    # Calculate 3D magnitude
    acc_magnitude = np.sqrt(acc_x**2 + acc_y**2 + acc_z**2)

    # Limit to first N minutes
    max_time = duration_minutes * 60  # Convert to seconds

    # Filter ECG data
    ecg_mask = ecg_time <= max_time
    ecg_signal = ecg_signal[ecg_mask]
    ecg_time = ecg_time[ecg_mask]

    # Filter ACC data
    acc_mask = acc_time <= max_time
    acc_magnitude = acc_magnitude[acc_mask]
    acc_time = acc_time[acc_mask]

    return ecg_signal, ecg_time, acc_magnitude, acc_time


def preprocess_signal(signal_data, lowcut, highcut, fs, order=4):
    """Apply bandpass filter to signal."""
    nyquist = 0.5 * fs
    low = lowcut / nyquist
    high = highcut / nyquist
    b, a = signal.butter(order, [low, high], btype="band")
    return signal.filtfilt(b, a, signal_data)


def apply_dwt_reconstruction(signal_data, wavelet="db4", level=6):
    """Apply DWT decomposition and reconstruction for denoising."""
    coeffs = pywt.wavedec(signal_data, wavelet, level=level)
    # Soft thresholding for denoising
    threshold = 0.1 * np.max([np.max(np.abs(c)) for c in coeffs[1:]])
    coeffs_thresh = [coeffs[0]] + [
        pywt.threshold(c, threshold, "soft") for c in coeffs[1:]
    ]
    reconstructed = pywt.waverec(coeffs_thresh, wavelet)
    return reconstructed


def find_r_peaks_ecg(ecg_signal, fs, height_threshold=None):
    """Find R peaks in ECG signal."""
    if height_threshold is None:
        height_threshold = np.std(ecg_signal) * 2

    peaks, _ = signal.find_peaks(
        ecg_signal,
        distance=int(fs * 0.3),  # Minimum 600ms between peaks
        height=height_threshold,
        prominence=height_threshold * 0.2,
    )
    return peaks


def find_j_peaks_bcg(bcg_signal, fs, threshold_factor=0.8, min_distance_sec=0.4):
    """
    Find J peaks in BCG signal using improved envelope-based detection with positive amplitude filtering.

    Parameters:
    - bcg_signal: BCG signal array (should be bandpass filtered)
    - fs: sampling frequency
    - method: 'hilbert' or 'rectify' for envelope calculation
    - threshold_factor: multiplier for adaptive threshold (default: 0.8, optimized for ECG matching)
    - min_distance_sec: minimum distance between peaks in seconds (default: 0.3, optimized for ECG matching)

    Steps:
    1. Envelope formation (Hilbert transform or Rectify + Lowpass 2 Hz)
    2. Peak detection (MinDistance 0.4s, adaptive threshold)
    3. Positive amplitude filtering (only keep peaks where bcg_signal[peak_idx] > 0)
    """

    # Step 1: Calculate envelope
    analytic_signal = signal.hilbert(bcg_signal)
    envelope = np.abs(analytic_signal)

    # Step 2: Smooth envelope
    win_samples = int(0.1 * fs)
    if win_samples % 2 == 0:
        win_samples += 1
    kernel = np.ones(win_samples) / win_samples
    env_smooth = np.convolve(envelope, kernel, mode="same")

    # Step 3: Adaptive Threshold
    env_masked = env_smooth.copy()
    env_masked[bcg_signal <= 0] = 0
    mu, sigma = env_smooth.mean(), env_smooth.std()
    thr = mu + threshold_factor * sigma

    # Step 4: Peak-Detection on envelope
    min_dist = int(min_distance_sec * fs)
    peaks, _ = signal.find_peaks(env_masked, height=thr, distance=min_dist)

    # Step 5: Filter for positive amplitude J peaks
    # Check the original bandpass-filtered signal at peak locations
    # positive_peaks = []
    # for peak_idx in peaks_envelope:
    #     if peak_idx < len(bcg_signal) and bcg_signal[peak_idx] > 0:
    #         positive_peaks.append(peak_idx)

    # peaks_idx = np.array(positive_peaks)

    return np.array(peaks), env_masked


def calculate_rr_intervals(peaks, fs):
    """Calculate RR intervals from peak indices."""
    if len(peaks) < 2:
        return np.array([])

    # Calculate intervals in milliseconds
    rr_intervals = np.diff(peaks) * (1000 / fs)
    return rr_intervals


def calculate_heart_rate(rr_intervals, smooth=False):
    """Calculate instantaneous heart rate from RR intervals."""
    if len(rr_intervals) == 0:
        return np.array([])

    # Convert RR intervals (ms) to heart rate (bpm)
    hr = 60000 / rr_intervals  # 60000 ms = 1 minute

    # Step 3: HR curve smoothing (3-beat moving average) if requested
    if smooth and len(hr) >= 3:
        hr = smooth_hr_curve(hr, window_size=3)

    return hr


def smooth_hr_curve(hr_values, window_size=3):
    """
    Smooth heart rate curve using moving average.

    Parameters:
    - hr_values: array of heart rate values
    - window_size: size of moving average window (default: 3 beats)
    """
    if len(hr_values) < window_size:
        return hr_values

    # Apply moving average
    smoothed_hr = np.convolve(
        hr_values, np.ones(window_size) / window_size, mode="valid"
    )

    # Pad the beginning and end to maintain original length
    pad_start = (window_size - 1) // 2
    pad_end = window_size - 1 - pad_start

    # Pad with edge values
    if pad_start > 0:
        start_pad = np.full(pad_start, hr_values[0])
        smoothed_hr = np.concatenate([start_pad, smoothed_hr])

    if pad_end > 0:
        end_pad = np.full(pad_end, hr_values[-1])
        smoothed_hr = np.concatenate([smoothed_hr, end_pad])

    return smoothed_hr


def calculate_hrv_metrics(rr_intervals):
    """Calculate various HRV metrics from RR intervals."""
    if len(rr_intervals) < 2:
        return {}

    # Time domain metrics
    rr_mean = np.mean(rr_intervals)
    rr_std = np.std(rr_intervals)

    # RMSSD: Root mean square of successive differences
    diff_rr = np.diff(rr_intervals)
    rmssd = np.sqrt(np.mean(diff_rr**2))

    # pNN50: Percentage of successive RR intervals that differ by more than 50ms
    nn50 = np.sum(np.abs(diff_rr) > 50)
    pnn50 = (nn50 / len(diff_rr)) * 100 if len(diff_rr) > 0 else 0

    # Triangular index (simplified)
    hist, _ = np.histogram(rr_intervals, bins=50)
    tri_index = len(rr_intervals) / np.max(hist) if np.max(hist) > 0 else 0

    hrv_metrics = {
        "mean_rr": rr_mean,
        "std_rr": rr_std,
        "rmssd": rmssd,
        "pnn50": pnn50,
        "triangular_index": tri_index,
        "cv": (rr_std / rr_mean) * 100
        if rr_mean > 0
        else 0,  # Coefficient of variation
    }

    return hrv_metrics


def calculate_frequency_domain_hrv(rr_intervals, rr_times=None):
    """Calculate frequency domain HRV metrics."""
    if len(rr_intervals) < 10:
        return {}

    # Create time vector if not provided
    if rr_times is None:
        rr_times = (
            np.cumsum(np.concatenate([[0], rr_intervals[:-1]])) / 1000
        )  # Convert to seconds

    # Interpolate RR intervals to regular time grid
    fs_interp = 4  # 4 Hz interpolation
    time_interp = np.arange(rr_times[0], rr_times[-1], 1 / fs_interp)

    if len(time_interp) < 10:
        return {}

    # Interpolate RR intervals
    f_interp = interpolate.interp1d(
        rr_times,
        rr_intervals,
        kind="cubic",
        bounds_error=False,
        fill_value="extrapolate",
    )
    rr_interp = f_interp(time_interp)

    # Remove trend (detrend)
    rr_detrend = signal.detrend(rr_interp)

    # Apply window
    window = signal.windows.hann(len(rr_detrend))
    rr_windowed = rr_detrend * window

    # Calculate power spectral density
    freqs, psd = signal.welch(
        rr_windowed, fs=fs_interp, nperseg=min(256, len(rr_windowed) // 2)
    )

    # Define frequency bands
    vlf_band = (0.0033, 0.04)  # Very low frequency
    lf_band = (0.04, 0.15)  # Low frequency
    hf_band = (0.15, 0.4)  # High frequency

    # Calculate power in each band
    vlf_power = np.trapz(
        psd[(freqs >= vlf_band[0]) & (freqs < vlf_band[1])],
        freqs[(freqs >= vlf_band[0]) & (freqs < vlf_band[1])],
    )
    lf_power = np.trapz(
        psd[(freqs >= lf_band[0]) & (freqs < lf_band[1])],
        freqs[(freqs >= lf_band[0]) & (freqs < lf_band[1])],
    )
    hf_power = np.trapz(
        psd[(freqs >= hf_band[0]) & (freqs < hf_band[1])],
        freqs[(freqs >= hf_band[0]) & (freqs < hf_band[1])],
    )

    total_power = vlf_power + lf_power + hf_power
    lf_hf_ratio = lf_power / hf_power if hf_power > 0 else 0

    freq_metrics = {
        "vlf_power": vlf_power,
        "lf_power": lf_power,
        "hf_power": hf_power,
        "total_power": total_power,
        "lf_hf_ratio": lf_hf_ratio,
        "lf_norm": (lf_power / (lf_power + hf_power)) * 100
        if (lf_power + hf_power) > 0
        else 0,
        "hf_norm": (hf_power / (lf_power + hf_power)) * 100
        if (lf_power + hf_power) > 0
        else 0,
    }

    return freq_metrics, freqs, psd


def plot_signals(time1, signal1, time2, signal2, title1, title2, ylabel1, ylabel2):
    """Plot two signals in subplots."""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

    ax1.plot(time1, signal1)
    ax1.set_title(title1)
    ax1.set_ylabel(ylabel1)
    ax1.grid(True)

    ax2.plot(time2, signal2)
    ax2.set_title(title2)
    ax2.set_xlabel("Time (s)")
    ax2.set_ylabel(ylabel2)
    ax2.grid(True)

    plt.tight_layout()
    return fig


def plot_peaks_detection(time, signal, peaks, title, ylabel):
    """Plot signal with detected peaks."""
    fig, ax = plt.subplots(figsize=(12, 6))
    ax.plot(time, signal, label="Signal")
    ax.plot(time[peaks], signal[peaks], "ro", markersize=8, label="Detected Peaks")
    ax.set_title(title)
    ax.set_xlabel("Time (s)")
    ax.set_ylabel(ylabel)
    ax.legend()
    ax.grid(True)
    return fig


def plot_envelope_peaks_detection(time, signal, envelope, peaks, title, ylabel):
    """Plot signal and envelope with peaks detected on envelope only."""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

    # Plot original signal without peaks
    ax1.plot(time, signal, "b-", alpha=0.7, label="Original Signal")
    ax1.set_title(f"{title} - Original Signal")
    ax1.set_ylabel(ylabel)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot envelope with peaks
    ax2.plot(time, envelope, "g-", linewidth=2, label="Signal Envelope")
    ax2.plot(time[peaks], envelope[peaks], "ro", markersize=8, label="Detected Peaks")
    ax2.set_title(f"{title} - Envelope with Peak Detection")
    ax2.set_xlabel("Time (s)")
    ax2.set_ylabel("Envelope Amplitude")
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    return fig


def plot_hr_comparison(ecg_hr, bcg_hr, ecg_times, bcg_times):
    """Plot heart rate comparison between ECG and BCG."""
    fig, ax = plt.subplots(figsize=(12, 6))

    ax.plot(ecg_times, ecg_hr, "r-", label="ECG HR", linewidth=2, alpha=0.8)
    ax.plot(bcg_times, bcg_hr, "b-", label="BCG HR", linewidth=2, alpha=0.8)

    ax.set_title("Heart Rate Comparison: ECG vs BCG")
    ax.set_xlabel("Time (s)")
    ax.set_ylabel("Heart Rate (bpm)")
    ax.legend()
    ax.grid(True)

    plt.tight_layout()
    return fig


def plot_hrv_psd(freqs, psd, title="HRV Power Spectral Density"):
    """Plot HRV power spectral density."""
    fig, ax = plt.subplots(figsize=(10, 6))

    ax.semilogy(freqs, psd)
    ax.set_xlabel("Frequency (Hz)")
    ax.set_ylabel("Power Spectral Density")
    ax.set_title(title)
    ax.grid(True)

    # Mark frequency bands
    ax.axvspan(0.0033, 0.04, alpha=0.3, color="red", label="VLF")
    ax.axvspan(0.04, 0.15, alpha=0.3, color="green", label="LF")
    ax.axvspan(0.15, 0.4, alpha=0.3, color="blue", label="HF")
    ax.legend()

    plt.tight_layout()
    return fig


def plot_bcg_envelope_detection(
    time, signal, envelope, peaks, title="BCG Envelope-based Peak Detection"
):
    """
    Plot BCG signal with envelope and detected peaks.

    Parameters:
    - time: time vector
    - signal: original BCG signal
    - envelope: calculated envelope
    - peaks: detected peak indices
    - title: plot title
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

    # Plot original signal (without peaks)
    ax1.plot(time, signal, "b-", alpha=0.7, label="Original BCG Signal")
    ax1.set_title(f"{title} - Original Signal")
    ax1.set_ylabel("Amplitude (ADC units)")
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot envelope with peaks
    ax2.plot(time, envelope, "g-", linewidth=2, label="Signal Envelope")
    ax2.plot(time[peaks], envelope[peaks], "ro", markersize=8, label="Detected Peaks")

    # Add threshold line
    envelope_mean = np.mean(envelope)
    envelope_std = np.std(envelope)
    threshold = envelope_mean + 1.5 * envelope_std
    ax2.axhline(
        threshold,
        color="red",
        linestyle="--",
        alpha=0.7,
        label=f"Adaptive Threshold: {threshold:.1f}",
    )

    ax2.set_title(f"{title} - Envelope with Peak Detection")
    ax2.set_xlabel("Time (s)")
    ax2.set_ylabel("Envelope Amplitude")
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    return fig


def plot_hr_smoothing_comparison(
    time, hr_original, hr_smoothed, title="Heart Rate Smoothing Comparison"
):
    """
    Plot comparison between original and smoothed heart rate.

    Parameters:
    - time: time vector
    - hr_original: original heart rate values
    - hr_smoothed: smoothed heart rate values
    - title: plot title
    """
    fig, ax = plt.subplots(figsize=(12, 6))

    ax.plot(
        time,
        hr_original,
        "b-",
        alpha=0.6,
        linewidth=1,
        label="Original HR",
        marker="o",
        markersize=4,
    )
    ax.plot(time, hr_smoothed, "r-", linewidth=2, label="Smoothed HR (3-beat MA)")

    ax.set_title(title)
    ax.set_xlabel("Time (s)")
    ax.set_ylabel("Heart Rate (bpm)")
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Add statistics
    if len(hr_original) > 0 and len(hr_smoothed) > 0:
        ax.text(
            0.02,
            0.98,
            f"Original HR: {np.mean(hr_original):.1f} ± {np.std(hr_original):.1f} bpm\n"
            f"Smoothed HR: {np.mean(hr_smoothed):.1f} ± {np.std(hr_smoothed):.1f} bpm",
            transform=ax.transAxes,
            verticalalignment="top",
            bbox=dict(boxstyle="round", facecolor="wheat", alpha=0.8),
        )

    plt.tight_layout()
    return fig
