#!/usr/bin/env python3
"""
Demonstration script for improved BCG peak detection algorithm.

This script demonstrates the three-step improvement:
1. Envelope formation (Hilbert transform or Rectify + Lowpass 2 Hz)
2. Peak detection (MinDistance 0.4s, adaptive threshold)
3. HR curve smoothing (3-beat moving average)
"""

import numpy as np
import matplotlib.pyplot as plt
from hw4_utils import *


def demo_improved_bcg_detection():
    """Demonstrate the improved BCG peak detection algorithm."""

    print("=== Improved BCG Peak Detection Demonstration ===\n")

    # Load data
    ecg_file = "../data/lab4/AB4_ecg.txt"
    acc_file = "../data/lab4/AB4_acc.txt"

    print("1. Loading and preprocessing data...")
    ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(
        ecg_file,
        acc_file,
        duration_minutes=2,  # Use 2 minutes for demo
    )

    # Calculate sampling frequencies
    fs_ecg = 1 / np.mean(np.diff(ecg_time))
    fs_bcg = 1 / np.mean(np.diff(bcg_time))

    print(f"   ✓ Data loaded: ECG {fs_ecg:.1f} Hz, BCG {fs_bcg:.1f} Hz")

    # Preprocess signals
    ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
    bcg_filtered = preprocess_signal(bcg_signal, lowcut=1, highcut=20, fs=fs_bcg)

    # Apply DWT
    ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet="db4", level=6)
    bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet="db6", level=8)

    # Ensure same length
    min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
    min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))

    ecg_dwt = ecg_dwt[:min_len_ecg]
    bcg_dwt = bcg_dwt[:min_len_bcg]
    ecg_time_dwt = ecg_time[:min_len_ecg]
    bcg_time_dwt = bcg_time[:min_len_bcg]

    print("   ✓ Preprocessing completed")

    print("\n2. Comparing peak detection methods...")

    # ECG peak detection (reference)
    ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
    print(f"   ✓ ECG R peaks: {len(ecg_peaks)} detected")

    # BCG peak detection - Hilbert method
    bcg_peaks_hilbert, envelope_hilbert = find_j_peaks_bcg(
        bcg_dwt, fs_bcg, method="hilbert"
    )
    print(f"   ✓ BCG J peaks (Hilbert): {len(bcg_peaks_hilbert)} detected")

    # BCG peak detection - Rectify + Lowpass method
    bcg_peaks_rectify, envelope_rectify = find_j_peaks_bcg(
        bcg_dwt, fs_bcg, method="rectify"
    )
    print(f"   ✓ BCG J peaks (Rectify+LP): {len(bcg_peaks_rectify)} detected")

    print("\n3. Analyzing heart rate with smoothing...")

    # Calculate RR intervals and heart rates
    rr_ecg = calculate_rr_intervals(ecg_peaks, fs_ecg)
    rr_bcg_hilbert = calculate_rr_intervals(bcg_peaks_hilbert, fs_bcg)
    rr_bcg_rectify = calculate_rr_intervals(bcg_peaks_rectify, fs_bcg)

    # Calculate heart rates (original and smoothed)
    hr_ecg = calculate_heart_rate(rr_ecg)
    hr_bcg_hilbert = calculate_heart_rate(rr_bcg_hilbert)
    hr_bcg_hilbert_smooth = calculate_heart_rate(rr_bcg_hilbert, smooth=True)
    hr_bcg_rectify = calculate_heart_rate(rr_bcg_rectify)
    hr_bcg_rectify_smooth = calculate_heart_rate(rr_bcg_rectify, smooth=True)

    # Print statistics
    if len(hr_ecg) > 0:
        print(f"   ✓ ECG HR: {np.mean(hr_ecg):.1f} ± {np.std(hr_ecg):.1f} bpm")
    if len(hr_bcg_hilbert) > 0:
        print(
            f"   ✓ BCG HR (Hilbert): {np.mean(hr_bcg_hilbert):.1f} ± {np.std(hr_bcg_hilbert):.1f} bpm"
        )
        print(
            f"   ✓ BCG HR (Hilbert, smoothed): {np.mean(hr_bcg_hilbert_smooth):.1f} ± {np.std(hr_bcg_hilbert_smooth):.1f} bpm"
        )
    if len(hr_bcg_rectify) > 0:
        print(
            f"   ✓ BCG HR (Rectify+LP): {np.mean(hr_bcg_rectify):.1f} ± {np.std(hr_bcg_rectify):.1f} bpm"
        )
        print(
            f"   ✓ BCG HR (Rectify+LP, smoothed): {np.mean(hr_bcg_rectify_smooth):.1f} ± {np.std(hr_bcg_rectify_smooth):.1f} bpm"
        )

    print("\n4. Creating visualizations...")

    # Visualization 1: Envelope comparison
    fig1, axes = plt.subplots(3, 1, figsize=(15, 12))

    # Original BCG signal
    time_zoom = bcg_time_dwt <= 60  # First 60 seconds
    axes[0].plot(
        bcg_time_dwt[time_zoom], bcg_dwt[time_zoom], "b-", alpha=0.7, label="BCG Signal"
    )
    axes[0].set_title("Original BCG Signal (First 60s)")
    axes[0].set_ylabel("Amplitude")
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # Hilbert envelope
    axes[1].plot(
        bcg_time_dwt[time_zoom],
        envelope_hilbert[time_zoom],
        "g-",
        linewidth=2,
        label="Hilbert Envelope",
    )
    hilbert_peaks_zoom = bcg_peaks_hilbert[bcg_time_dwt[bcg_peaks_hilbert] <= 60]
    axes[1].plot(
        bcg_time_dwt[hilbert_peaks_zoom],
        envelope_hilbert[hilbert_peaks_zoom],
        "ro",
        markersize=8,
        label="Detected Peaks",
    )
    axes[1].set_title("Hilbert Transform Envelope with Peak Detection")
    axes[1].set_ylabel("Envelope Amplitude")
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)

    # Rectify + Lowpass envelope
    axes[2].plot(
        bcg_time_dwt[time_zoom],
        envelope_rectify[time_zoom],
        "orange",
        linewidth=2,
        label="Rectify+LP Envelope",
    )
    rectify_peaks_zoom = bcg_peaks_rectify[bcg_time_dwt[bcg_peaks_rectify] <= 60]
    axes[2].plot(
        bcg_time_dwt[rectify_peaks_zoom],
        envelope_rectify[rectify_peaks_zoom],
        "mo",
        markersize=8,
        label="Detected Peaks",
    )
    axes[2].set_title("Rectify + Lowpass (2Hz) Envelope with Peak Detection")
    axes[2].set_xlabel("Time (s)")
    axes[2].set_ylabel("Envelope Amplitude")
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig("envelope_comparison.png", dpi=150, bbox_inches="tight")
    plt.show()

    # Visualization 2: Heart rate comparison with smoothing
    if len(hr_ecg) > 0 and len(hr_bcg_hilbert) > 0:
        fig2, axes = plt.subplots(2, 1, figsize=(15, 10))

        # Heart rate time series
        hr_times_ecg = ecg_time_dwt[ecg_peaks[1:]] if len(ecg_peaks) > 1 else []
        hr_times_bcg_hilbert = (
            bcg_time_dwt[bcg_peaks_hilbert[1:]] if len(bcg_peaks_hilbert) > 1 else []
        )

        if len(hr_times_ecg) > 0 and len(hr_times_bcg_hilbert) > 0:
            axes[0].plot(
                hr_times_ecg, hr_ecg, "r-", linewidth=2, label="ECG HR", alpha=0.8
            )
            axes[0].plot(
                hr_times_bcg_hilbert,
                hr_bcg_hilbert,
                "b-",
                alpha=0.6,
                linewidth=1,
                label="BCG HR (Hilbert, Original)",
                marker="o",
                markersize=3,
            )
            axes[0].plot(
                hr_times_bcg_hilbert,
                hr_bcg_hilbert_smooth,
                "g-",
                linewidth=2,
                label="BCG HR (Hilbert, 3-beat Smoothed)",
            )
            axes[0].set_title("Heart Rate Comparison: ECG vs BCG (Hilbert Method)")
            axes[0].set_ylabel("Heart Rate (bpm)")
            axes[0].legend()
            axes[0].grid(True, alpha=0.3)

        # Smoothing effect demonstration
        if len(hr_times_bcg_hilbert) > 0:
            axes[1].plot(
                hr_times_bcg_hilbert,
                hr_bcg_hilbert,
                "b-",
                alpha=0.6,
                linewidth=1,
                label="Original BCG HR",
                marker="o",
                markersize=4,
            )
            axes[1].plot(
                hr_times_bcg_hilbert,
                hr_bcg_hilbert_smooth,
                "r-",
                linewidth=2,
                label="3-beat Moving Average",
            )
            axes[1].set_title(
                "Effect of 3-beat Moving Average Smoothing on BCG Heart Rate"
            )
            axes[1].set_xlabel("Time (s)")
            axes[1].set_ylabel("Heart Rate (bpm)")
            axes[1].legend()
            axes[1].grid(True, alpha=0.3)

            # Add statistics
            axes[1].text(
                0.02,
                0.98,
                f"Original: {np.mean(hr_bcg_hilbert):.1f} ± {np.std(hr_bcg_hilbert):.1f} bpm\n"
                f"Smoothed: {np.mean(hr_bcg_hilbert_smooth):.1f} ± {np.std(hr_bcg_hilbert_smooth):.1f} bpm\n"
                f"Std Reduction: {((np.std(hr_bcg_hilbert) - np.std(hr_bcg_hilbert_smooth)) / np.std(hr_bcg_hilbert) * 100):.1f}%",
                transform=axes[1].transAxes,
                verticalalignment="top",
                bbox=dict(boxstyle="round", facecolor="wheat", alpha=0.8),
            )

        plt.tight_layout()
        plt.savefig("hr_smoothing_comparison.png", dpi=150, bbox_inches="tight")
        plt.show()

    # Visualization 3: Method comparison summary
    fig3, ax = plt.subplots(figsize=(12, 8))

    methods = [
        "ECG\n(Reference)",
        "BCG\n(Hilbert)",
        "BCG\n(Hilbert+Smooth)",
        "BCG\n(Rectify+LP)",
        "BCG\n(Rectify+LP+Smooth)",
    ]
    hr_means = []
    hr_stds = []

    if len(hr_ecg) > 0:
        hr_means.append(np.mean(hr_ecg))
        hr_stds.append(np.std(hr_ecg))
    else:
        hr_means.append(0)
        hr_stds.append(0)

    if len(hr_bcg_hilbert) > 0:
        hr_means.extend([np.mean(hr_bcg_hilbert), np.mean(hr_bcg_hilbert_smooth)])
        hr_stds.extend([np.std(hr_bcg_hilbert), np.std(hr_bcg_hilbert_smooth)])
    else:
        hr_means.extend([0, 0])
        hr_stds.extend([0, 0])

    if len(hr_bcg_rectify) > 0:
        hr_means.extend([np.mean(hr_bcg_rectify), np.mean(hr_bcg_rectify_smooth)])
        hr_stds.extend([np.std(hr_bcg_rectify), np.std(hr_bcg_rectify_smooth)])
    else:
        hr_means.extend([0, 0])
        hr_stds.extend([0, 0])

    colors = ["red", "blue", "green", "orange", "purple"]
    bars = ax.bar(
        methods,
        hr_means,
        yerr=hr_stds,
        capsize=5,
        color=colors,
        alpha=0.7,
        edgecolor="black",
    )

    ax.set_title("Heart Rate Comparison: Different BCG Processing Methods")
    ax.set_ylabel("Heart Rate (bpm)")
    ax.grid(True, alpha=0.3, axis="y")

    # Add value labels on bars
    for bar, mean, std in zip(bars, hr_means, hr_stds):
        if mean > 0:
            ax.text(
                bar.get_x() + bar.get_width() / 2,
                bar.get_height() + std + 1,
                f"{mean:.1f}±{std:.1f}",
                ha="center",
                va="bottom",
                fontweight="bold",
            )

    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig("method_comparison.png", dpi=150, bbox_inches="tight")
    plt.show()

    print("\n=== Demonstration completed! ===")
    print("\nKey improvements implemented:")
    print("1. ✓ Envelope formation using Hilbert transform or Rectify+Lowpass (2Hz)")
    print("2. ✓ Adaptive peak detection with 0.4s minimum distance")
    print("3. ✓ 3-beat moving average smoothing for HR curve")
    print("\nVisualization files saved:")
    print("- envelope_comparison.png")
    print("- hr_smoothing_comparison.png")
    print("- method_comparison.png")

    return True


if __name__ == "__main__":
    demo_improved_bcg_detection()
