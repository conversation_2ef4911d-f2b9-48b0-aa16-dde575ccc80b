#!/usr/bin/env python3
"""
Script to optimize J peak detection parameters to match ECG peak counts.
Tests different parameter combinations and finds the best settings.
"""

import numpy as np
import matplotlib.pyplot as plt
from hw4_utils import *
import itertools

def test_j_peak_parameters():
    """Test different parameter combinations for J peak detection."""
    
    print("=== J Peak Detection Parameter Optimization ===\n")
    
    # Load data
    print("1. Loading data...")
    try:
        ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(
            '../data/lab4/AB4_ecg.txt', '../data/lab4/AB4_acc.txt', duration_minutes=5
        )
        fs_ecg = 1 / np.mean(np.diff(ecg_time))
        fs_bcg = 1 / np.mean(np.diff(bcg_time))
        print(f"   ✓ Data loaded: ECG {fs_ecg:.1f} Hz, BCG {fs_bcg:.1f} Hz")
    except Exception as e:
        print(f"   ✗ Error loading data: {e}")
        return None
    
    # Preprocess signals
    print("\n2. Preprocessing signals...")
    try:
        ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
        bcg_filtered = preprocess_signal(bcg_signal, lowcut=0.5, highcut=20, fs=fs_bcg)
        
        ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet='db4', level=4)
        bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet='db4', level=4)
        
        # Ensure same length
        min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
        min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))
        
        ecg_dwt = ecg_dwt[:min_len_ecg]
        bcg_dwt = bcg_dwt[:min_len_bcg]
        ecg_time_dwt = ecg_time[:min_len_ecg]
        bcg_time_dwt = bcg_time[:min_len_bcg]
        
        print(f"   ✓ Preprocessing completed")
    except Exception as e:
        print(f"   ✗ Error in preprocessing: {e}")
        return None
    
    # Get ECG reference
    print("\n3. Getting ECG reference...")
    try:
        ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
        ecg_count = len(ecg_peaks)
        print(f"   ✓ ECG R peaks: {ecg_count} detected")
        
        # Calculate expected heart rate
        if ecg_count > 1:
            duration_min = (ecg_time_dwt[-1] - ecg_time_dwt[0]) / 60
            avg_hr_ecg = ecg_count / duration_min
            print(f"   ✓ Average ECG HR: {avg_hr_ecg:.1f} bpm")
        
    except Exception as e:
        print(f"   ✗ Error in ECG peak detection: {e}")
        return None
    
    # Parameter ranges to test
    print("\n4. Testing parameter combinations...")
    
    # Define parameter ranges
    threshold_factors = [0.8, 1.0, 1.2, 1.5, 1.8, 2.0]
    min_distances = [0.3, 0.35, 0.4, 0.45, 0.5]
    methods = ['hilbert', 'rectify']
    
    results = []
    
    total_combinations = len(threshold_factors) * len(min_distances) * len(methods)
    print(f"   Testing {total_combinations} parameter combinations...")
    
    combination_count = 0
    
    for method in methods:
        for threshold_factor in threshold_factors:
            for min_distance in min_distances:
                combination_count += 1
                
                try:
                    # Test this parameter combination
                    bcg_peaks, bcg_envelope = find_j_peaks_bcg(
                        bcg_dwt, fs_bcg, 
                        method=method,
                        threshold_factor=threshold_factor,
                        min_distance_sec=min_distance
                    )
                    
                    bcg_count = len(bcg_peaks)
                    
                    # Calculate metrics
                    count_diff = abs(bcg_count - ecg_count)
                    count_ratio = bcg_count / ecg_count if ecg_count > 0 else 0
                    
                    # Calculate average heart rate from BCG
                    if bcg_count > 1:
                        duration_min_bcg = (bcg_time_dwt[-1] - bcg_time_dwt[0]) / 60
                        avg_hr_bcg = bcg_count / duration_min_bcg
                        hr_diff = abs(avg_hr_bcg - avg_hr_ecg) if ecg_count > 1 else float('inf')
                    else:
                        avg_hr_bcg = 0
                        hr_diff = float('inf')
                    
                    # Store results
                    results.append({
                        'method': method,
                        'threshold_factor': threshold_factor,
                        'min_distance': min_distance,
                        'bcg_count': bcg_count,
                        'ecg_count': ecg_count,
                        'count_diff': count_diff,
                        'count_ratio': count_ratio,
                        'avg_hr_bcg': avg_hr_bcg,
                        'avg_hr_ecg': avg_hr_ecg,
                        'hr_diff': hr_diff,
                        'score': count_diff + hr_diff * 0.1  # Combined score
                    })
                    
                    if combination_count % 10 == 0:
                        print(f"   Progress: {combination_count}/{total_combinations} ({combination_count/total_combinations*100:.1f}%)")
                
                except Exception as e:
                    print(f"   Warning: Error with {method}, thr={threshold_factor}, dist={min_distance}: {e}")
                    continue
    
    # Analyze results
    print(f"\n5. Analyzing results from {len(results)} successful combinations...")
    
    if not results:
        print("   ✗ No successful parameter combinations found!")
        return None
    
    # Sort by score (lower is better)
    results.sort(key=lambda x: x['score'])
    
    # Display top 10 results
    print("\n   Top 10 parameter combinations:")
    print("   " + "="*100)
    print(f"   {'Rank':<4} {'Method':<8} {'Thr':<5} {'Dist':<5} {'BCG':<4} {'ECG':<4} {'Diff':<4} {'Ratio':<6} {'HR_BCG':<6} {'HR_ECG':<6} {'Score':<6}")
    print("   " + "-"*100)
    
    for i, result in enumerate(results[:10]):
        print(f"   {i+1:<4} {result['method']:<8} {result['threshold_factor']:<5} {result['min_distance']:<5} "
              f"{result['bcg_count']:<4} {result['ecg_count']:<4} {result['count_diff']:<4} "
              f"{result['count_ratio']:<6.2f} {result['avg_hr_bcg']:<6.1f} {result['avg_hr_ecg']:<6.1f} {result['score']:<6.1f}")
    
    # Get best parameters
    best = results[0]
    print(f"\n   🏆 Best parameters:")
    print(f"      Method: {best['method']}")
    print(f"      Threshold factor: {best['threshold_factor']}")
    print(f"      Min distance: {best['min_distance']} seconds")
    print(f"      BCG peaks: {best['bcg_count']} (vs ECG: {best['ecg_count']})")
    print(f"      Peak count difference: {best['count_diff']}")
    print(f"      Peak count ratio: {best['count_ratio']:.2f}")
    print(f"      Heart rate difference: {best['hr_diff']:.1f} bpm")
    
    return best, results

def visualize_best_parameters(best_params):
    """Visualize the results with the best parameters."""
    
    print("\n6. Creating visualization with best parameters...")
    
    # Load and preprocess data again
    ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(
        '../data/lab4/AB4_ecg.txt', '../data/lab4/AB4_acc.txt', duration_minutes=2  # Shorter for visualization
    )
    fs_ecg = 1 / np.mean(np.diff(ecg_time))
    fs_bcg = 1 / np.mean(np.diff(bcg_time))
    
    ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
    bcg_filtered = preprocess_signal(bcg_signal, lowcut=0.5, highcut=20, fs=fs_bcg)
    
    ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet='db4', level=4)
    bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet='db4', level=4)
    
    min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
    min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))
    
    ecg_dwt = ecg_dwt[:min_len_ecg]
    bcg_dwt = bcg_dwt[:min_len_bcg]
    ecg_time_dwt = ecg_time[:min_len_ecg]
    bcg_time_dwt = bcg_time[:min_len_bcg]
    
    # Detect peaks with best parameters
    ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
    bcg_peaks, bcg_envelope = find_j_peaks_bcg(
        bcg_dwt, fs_bcg,
        method=best_params['method'],
        threshold_factor=best_params['threshold_factor'],
        min_distance_sec=best_params['min_distance']
    )
    
    # Create visualization
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    
    # Plot 1: ECG with R peaks
    axes[0].plot(ecg_time_dwt, ecg_dwt, 'b-', alpha=0.7, label='ECG Signal')
    axes[0].plot(ecg_time_dwt[ecg_peaks], ecg_dwt[ecg_peaks], 'ro', markersize=6, label=f'R Peaks ({len(ecg_peaks)})')
    axes[0].set_title('ECG Signal with R Peaks')
    axes[0].set_ylabel('Amplitude (mV)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Plot 2: BCG signal (no peaks shown)
    axes[1].plot(bcg_time_dwt, bcg_dwt, 'b-', alpha=0.7, label='BCG Signal')
    # Mark positive and negative regions
    positive_mask = bcg_dwt > 0
    axes[1].fill_between(bcg_time_dwt, 0, bcg_dwt, where=positive_mask, alpha=0.3, color='green', label='Positive Amplitude')
    axes[1].fill_between(bcg_time_dwt, 0, bcg_dwt, where=~positive_mask, alpha=0.3, color='red', label='Negative Amplitude')
    axes[1].set_title('BCG Signal (Bandpass Filtered) - Positive/Negative Regions')
    axes[1].set_ylabel('Magnitude (ADC units)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    axes[1].axhline(0, color='black', linestyle='-', alpha=0.5)
    
    # Plot 3: BCG envelope with J peaks
    axes[2].plot(bcg_time_dwt, bcg_envelope, 'g-', linewidth=2, label='Envelope')
    axes[2].plot(bcg_time_dwt[bcg_peaks], bcg_envelope[bcg_peaks], 'ro', markersize=8, label=f'J Peaks ({len(bcg_peaks)}) - Positive Only')
    
    # Add threshold line
    mu, sigma = bcg_envelope.mean(), bcg_envelope.std()
    threshold = mu + best_params['threshold_factor'] * sigma
    axes[2].axhline(threshold, color='orange', linestyle='--', alpha=0.8, label=f'Threshold (μ + {best_params["threshold_factor"]}σ)')
    
    axes[2].set_title(f'BCG Envelope with Optimized J Peak Detection\n'
                     f'Method: {best_params["method"]}, Threshold: {best_params["threshold_factor"]}, Min Distance: {best_params["min_distance"]}s')
    axes[2].set_xlabel('Time (s)')
    axes[2].set_ylabel('Envelope Amplitude')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('optimized_j_peak_detection.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"   ✓ Visualization saved as 'optimized_j_peak_detection.png'")

if __name__ == "__main__":
    # Run optimization
    best_params, all_results = test_j_peak_parameters()
    
    if best_params:
        # Create visualization
        visualize_best_parameters(best_params)
        
        print(f"\n=== Optimization Complete ===")
        print(f"Recommended parameters for find_j_peaks_bcg():")
        print(f"  method='{best_params['method']}'")
        print(f"  threshold_factor={best_params['threshold_factor']}")
        print(f"  min_distance_sec={best_params['min_distance']}")
        print(f"\nThis configuration achieves:")
        print(f"  - Peak count difference: {best_params['count_diff']} peaks")
        print(f"  - Peak count ratio: {best_params['count_ratio']:.2f}")
        print(f"  - Heart rate difference: {best_params['hr_diff']:.1f} bpm")
    else:
        print("Optimization failed!")
