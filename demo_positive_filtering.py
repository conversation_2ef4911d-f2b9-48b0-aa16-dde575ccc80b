#!/usr/bin/env python3
"""
Demonstration of positive amplitude filtering for J peak detection.
Shows the effect of filtering out negative amplitude peaks.
"""

import numpy as np
import matplotlib.pyplot as plt
from hw4_utils import *

def demo_positive_amplitude_filtering():
    """Demonstrate the positive amplitude filtering effect."""
    
    print("=== Positive Amplitude Filtering Demonstration ===\n")
    
    # Load data
    print("1. Loading and preprocessing data...")
    ecg_signal, ecg_time, bcg_signal, bcg_time = load_bcg_ecg_data(
        '../data/lab4/AB4_ecg.txt', '../data/lab4/AB4_acc.txt', duration_minutes=2
    )
    fs_ecg = 1 / np.mean(np.diff(ecg_time))
    fs_bcg = 1 / np.mean(np.diff(bcg_time))
    
    # Preprocess
    ecg_filtered = preprocess_signal(ecg_signal, lowcut=0.5, highcut=40, fs=fs_ecg)
    bcg_filtered = preprocess_signal(bcg_signal, lowcut=0.5, highcut=20, fs=fs_bcg)
    
    ecg_dwt = apply_dwt_reconstruction(ecg_filtered, wavelet='db4', level=4)
    bcg_dwt = apply_dwt_reconstruction(bcg_filtered, wavelet='db4', level=4)
    
    min_len_ecg = min(len(ecg_filtered), len(ecg_dwt))
    min_len_bcg = min(len(bcg_filtered), len(bcg_dwt))
    
    ecg_dwt = ecg_dwt[:min_len_ecg]
    bcg_dwt = bcg_dwt[:min_len_bcg]
    ecg_time_dwt = ecg_time[:min_len_ecg]
    bcg_time_dwt = bcg_time[:min_len_bcg]
    
    print(f"   ✓ Data processed: ECG {fs_ecg:.1f} Hz, BCG {fs_bcg:.1f} Hz")
    
    # Get ECG reference
    ecg_peaks = find_r_peaks_ecg(ecg_dwt, fs_ecg)
    print(f"   ✓ ECG R peaks: {len(ecg_peaks)} detected")
    
    # Get BCG peaks with optimized parameters
    bcg_peaks, bcg_envelope = find_j_peaks_bcg(bcg_dwt, fs_bcg)
    print(f"   ✓ BCG J peaks (positive only): {len(bcg_peaks)} detected")
    
    # Simulate what would happen without positive filtering
    # We need to manually implement the envelope detection without filtering
    print("\n2. Analyzing positive amplitude filtering effect...")
    
    # Calculate envelope
    analytic_signal = signal.hilbert(bcg_dwt)
    envelope = np.abs(analytic_signal)
    
    # Smooth envelope
    win_samples = int(0.1 * fs_bcg)
    if win_samples % 2 == 0:
        win_samples += 1
    kernel = np.ones(win_samples) / win_samples
    env_smooth = np.convolve(envelope, kernel, mode="same")
    
    # Find all envelope peaks (without positive filtering)
    mu, sigma = env_smooth.mean(), env_smooth.std()
    thr = mu + 0.8 * sigma  # Use optimized threshold
    min_dist = int(0.3 * fs_bcg)  # Use optimized distance
    
    all_envelope_peaks, _ = signal.find_peaks(env_smooth, height=thr, distance=min_dist)
    
    # Separate positive and negative amplitude peaks
    positive_peaks = []
    negative_peaks = []
    
    for peak_idx in all_envelope_peaks:
        if peak_idx < len(bcg_dwt):
            if bcg_dwt[peak_idx] > 0:
                positive_peaks.append(peak_idx)
            else:
                negative_peaks.append(peak_idx)
    
    positive_peaks = np.array(positive_peaks)
    negative_peaks = np.array(negative_peaks)
    
    print(f"   ✓ Total envelope peaks: {len(all_envelope_peaks)}")
    print(f"   ✓ Positive amplitude peaks: {len(positive_peaks)}")
    print(f"   ✓ Negative amplitude peaks: {len(negative_peaks)}")
    print(f"   ✓ Filtering effectiveness: {len(negative_peaks)} peaks removed ({len(negative_peaks)/len(all_envelope_peaks)*100:.1f}%)")
    
    # Create comprehensive visualization
    print("\n3. Creating visualization...")
    
    fig, axes = plt.subplots(4, 1, figsize=(15, 16))
    
    # Plot 1: ECG with R peaks
    axes[0].plot(ecg_time_dwt, ecg_dwt, 'b-', alpha=0.7, label='ECG Signal')
    axes[0].plot(ecg_time_dwt[ecg_peaks], ecg_dwt[ecg_peaks], 'ro', markersize=6, label=f'R Peaks ({len(ecg_peaks)})')
    axes[0].set_title('ECG Signal with R Peaks (Reference)')
    axes[0].set_ylabel('Amplitude (mV)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Plot 2: BCG signal with positive/negative regions
    axes[1].plot(bcg_time_dwt, bcg_dwt, 'b-', alpha=0.7, label='BCG Signal')
    positive_mask = bcg_dwt > 0
    axes[1].fill_between(bcg_time_dwt, 0, bcg_dwt, where=positive_mask, alpha=0.3, color='green', label='Positive Amplitude (J peaks)')
    axes[1].fill_between(bcg_time_dwt, 0, bcg_dwt, where=~positive_mask, alpha=0.3, color='red', label='Negative Amplitude (filtered out)')
    axes[1].axhline(0, color='black', linestyle='-', alpha=0.5)
    axes[1].set_title('BCG Signal - Positive vs Negative Amplitude Regions')
    axes[1].set_ylabel('Magnitude (ADC units)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # Plot 3: Envelope with all peaks (before filtering)
    axes[2].plot(bcg_time_dwt, env_smooth, 'g-', linewidth=2, label='Envelope')
    axes[2].plot(bcg_time_dwt[all_envelope_peaks], env_smooth[all_envelope_peaks], 'ko', markersize=6, alpha=0.7, label=f'All Envelope Peaks ({len(all_envelope_peaks)})')
    axes[2].plot(bcg_time_dwt[positive_peaks], env_smooth[positive_peaks], 'go', markersize=8, label=f'Positive Peaks ({len(positive_peaks)})')
    axes[2].plot(bcg_time_dwt[negative_peaks], env_smooth[negative_peaks], 'ro', markersize=8, label=f'Negative Peaks ({len(negative_peaks)})')
    axes[2].axhline(thr, color='orange', linestyle='--', alpha=0.8, label=f'Threshold (μ + 0.8σ)')
    axes[2].set_title('Envelope Peak Detection - Before Positive Amplitude Filtering')
    axes[2].set_ylabel('Envelope Amplitude')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    # Plot 4: Final result with only positive peaks
    axes[3].plot(bcg_time_dwt, env_smooth, 'g-', linewidth=2, label='Envelope')
    axes[3].plot(bcg_time_dwt[positive_peaks], env_smooth[positive_peaks], 'ro', markersize=8, label=f'J Peaks (Positive Only) ({len(positive_peaks)})')
    axes[3].axhline(thr, color='orange', linestyle='--', alpha=0.8, label=f'Threshold (μ + 0.8σ)')
    axes[3].set_title('Final J Peak Detection - After Positive Amplitude Filtering')
    axes[3].set_xlabel('Time (s)')
    axes[3].set_ylabel('Envelope Amplitude')
    axes[3].legend()
    axes[3].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('positive_amplitude_filtering_demo.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("   ✓ Visualization saved as 'positive_amplitude_filtering_demo.png'")
    
    # Summary statistics
    print("\n4. Summary Statistics:")
    print(f"   ECG R peaks (reference): {len(ecg_peaks)}")
    print(f"   BCG envelope peaks (all): {len(all_envelope_peaks)}")
    print(f"   BCG J peaks (positive only): {len(positive_peaks)}")
    print(f"   Peaks filtered out: {len(negative_peaks)}")
    print(f"   Peak count improvement: {abs(len(positive_peaks) - len(ecg_peaks))} vs {abs(len(all_envelope_peaks) - len(ecg_peaks))} difference from ECG")
    print(f"   Positive filtering reduces error by: {abs(len(all_envelope_peaks) - len(ecg_peaks)) - abs(len(positive_peaks) - len(ecg_peaks))} peaks")
    
    # Heart rate comparison
    duration_min = (bcg_time_dwt[-1] - bcg_time_dwt[0]) / 60
    hr_ecg = len(ecg_peaks) / duration_min
    hr_bcg_all = len(all_envelope_peaks) / duration_min
    hr_bcg_positive = len(positive_peaks) / duration_min
    
    print(f"\n5. Heart Rate Comparison:")
    print(f"   ECG HR: {hr_ecg:.1f} bpm")
    print(f"   BCG HR (all peaks): {hr_bcg_all:.1f} bpm (error: {abs(hr_bcg_all - hr_ecg):.1f} bpm)")
    print(f"   BCG HR (positive only): {hr_bcg_positive:.1f} bpm (error: {abs(hr_bcg_positive - hr_ecg):.1f} bpm)")
    print(f"   Heart rate error reduction: {abs(hr_bcg_all - hr_ecg) - abs(hr_bcg_positive - hr_ecg):.1f} bpm")
    
    return {
        'ecg_peaks': len(ecg_peaks),
        'all_envelope_peaks': len(all_envelope_peaks),
        'positive_peaks': len(positive_peaks),
        'negative_peaks': len(negative_peaks),
        'hr_ecg': hr_ecg,
        'hr_bcg_all': hr_bcg_all,
        'hr_bcg_positive': hr_bcg_positive
    }

if __name__ == "__main__":
    results = demo_positive_amplitude_filtering()
    
    print(f"\n=== Positive Amplitude Filtering Complete ===")
    print(f"Key improvements achieved:")
    print(f"✓ Removed {results['negative_peaks']} false peaks from negative amplitudes")
    print(f"✓ Improved peak count accuracy by {results['all_envelope_peaks'] - results['positive_peaks']} peaks")
    print(f"✓ Reduced heart rate error by {abs(results['hr_bcg_all'] - results['hr_ecg']) - abs(results['hr_bcg_positive'] - results['hr_ecg']):.1f} bpm")
    print(f"✓ Final BCG peak count: {results['positive_peaks']} (vs ECG: {results['ecg_peaks']})")
    
    print(f"\nOptimized parameters:")
    print(f"  method='hilbert'")
    print(f"  threshold_factor=0.8")
    print(f"  min_distance_sec=0.3")
    print(f"  + positive amplitude filtering")
